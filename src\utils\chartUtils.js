// Chart Utilities for Assessment Visualizations
// Utilities for creating various charts and visualizations

import Chart from 'chart.js/auto';
import { VIA_IS_CATEGORIES, VIA_IS_STRENGTH_DETAILS } from './assessmentDetails.js';

export class ChartUtils {
  
  // Create OCEAN radar chart
  static createOceanRadarChart(ctx, oceanData) {
    if (!ctx) return null;

    const data = {
      labels: [
        'Openness\n(<PERSON><PERSON><PERSON><PERSON><PERSON>)',
        'Conscientiousness\n(Kehati-hatian)',
        'Extraversion\n(Ekstraversi)',
        'Agreeableness\n(<PERSON><PERSON><PERSON>)',
        'Neuroticism\n(Neurotisisme)'
      ],
      datasets: [{
        label: 'OCEAN Profile',
        data: [
          oceanData.openness,
          oceanData.conscientiousness,
          oceanData.extraversion,
          oceanData.agreeableness,
          oceanData.neuroticism
        ],
        fill: true,
        backgroundColor: 'rgba(34, 197, 94, 0.2)',
        borderColor: 'rgb(34, 197, 94)',
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(34, 197, 94)',
        borderWidth: 2,
        pointRadius: 4
      }]
    };

    const config = {
      type: 'radar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return `${context.parsed.r}%`;
              }
            }
          }
        },
        scales: {
          r: {
            beginAtZero: true,
            max: 100,
            ticks: {
              stepSize: 20,
              font: {
                size: 10
              }
            },
            pointLabels: {
              font: {
                size: 11
              }
            }
          }
        }
      }
    };

    return new Chart(ctx, config);
  }

  // Create RIASEC radar chart
  static createRiasecRadarChart(ctx, riasecData) {
    if (!ctx) return null;

    const data = {
      labels: [
        'Realistic\n(Praktis)',
        'Investigative\n(Analitis)',
        'Artistic\n(Kreatif)',
        'Social\n(Sosial)',
        'Enterprising\n(Wirausaha)',
        'Conventional\n(Terorganisir)'
      ],
      datasets: [{
        label: 'RIASEC Profile',
        data: [
          riasecData.realistic,
          riasecData.investigative,
          riasecData.artistic,
          riasecData.social,
          riasecData.enterprising,
          riasecData.conventional
        ],
        fill: true,
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgb(59, 130, 246)',
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(59, 130, 246)',
        borderWidth: 2,
        pointRadius: 4
      }]
    };

    const config = {
      type: 'radar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return `${context.parsed.r}%`;
              }
            }
          }
        },
        scales: {
          r: {
            beginAtZero: true,
            max: 100,
            ticks: {
              stepSize: 20,
              font: {
                size: 10
              }
            },
            pointLabels: {
              font: {
                size: 11
              }
            }
          }
        }
      }
    };

    return new Chart(ctx, config);
  }

  // Create ViaIS categories radar chart
  static createViaIsCategoriesRadarChart(ctx, viaIsData) {
    if (!ctx) return null;

    // Calculate category averages
    const categoryScores = {};
    Object.entries(VIA_IS_CATEGORIES).forEach(([key, category]) => {
      const scores = category.strengths.map(strength => viaIsData[strength] || 0);
      categoryScores[key] = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    });

    const data = {
      labels: [
        'Wisdom &\nKnowledge',
        'Courage',
        'Humanity',
        'Justice',
        'Temperance',
        'Transcendence'
      ],
      datasets: [{
        label: 'VIA-IS Categories',
        data: [
          categoryScores.wisdom,
          categoryScores.courage,
          categoryScores.humanity,
          categoryScores.justice,
          categoryScores.temperance,
          categoryScores.transcendence
        ],
        fill: true,
        backgroundColor: 'rgba(168, 85, 247, 0.2)',
        borderColor: 'rgb(168, 85, 247)',
        pointBackgroundColor: 'rgb(168, 85, 247)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(168, 85, 247)',
        borderWidth: 2,
        pointRadius: 4
      }]
    };

    const config = {
      type: 'radar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return `${Math.round(context.parsed.r)}%`;
              }
            }
          }
        },
        scales: {
          r: {
            beginAtZero: true,
            max: 100,
            ticks: {
              stepSize: 20,
              font: {
                size: 10
              }
            },
            pointLabels: {
              font: {
                size: 11
              }
            }
          }
        }
      }
    };

    return new Chart(ctx, config);
  }

  // Create ViaIS strengths bar chart
  static createViaIsStrengthsBarChart(ctx, viaIsData) {
    if (!ctx) return null;

    // Convert to array and sort by score
    const strengthsArray = Object.entries(viaIsData).map(([key, value]) => ({
      name: key,
      score: value,
      displayName: VIA_IS_STRENGTH_DETAILS[key]?.name || key
    })).sort((a, b) => b.score - a.score);

    const data = {
      labels: strengthsArray.map(s => s.displayName),
      datasets: [{
        label: 'Character Strengths',
        data: strengthsArray.map(s => s.score),
        backgroundColor: strengthsArray.map((_, index) => {
          // Color gradient from highest to lowest
          const hue = 270 - (index * 10); // Purple to blue gradient
          return `hsla(${hue}, 70%, 60%, 0.8)`;
        }),
        borderColor: strengthsArray.map((_, index) => {
          const hue = 270 - (index * 10);
          return `hsla(${hue}, 70%, 50%, 1)`;
        }),
        borderWidth: 1
      }]
    };

    const config = {
      type: 'bar',
      data: data,
      options: {
        indexAxis: 'y',
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return `${context.parsed.x}%`;
              }
            }
          }
        },
        scales: {
          x: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          },
          y: {
            ticks: {
              font: {
                size: 10
              }
            }
          }
        }
      }
    };

    return new Chart(ctx, config);
  }

  // Get color classes for ViaIS categories
  static getViaIsCategoryColorClasses(color) {
    const colorMap = {
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      red: 'bg-red-50 border-red-200 text-red-800',
      pink: 'bg-pink-50 border-pink-200 text-pink-800',
      green: 'bg-green-50 border-green-200 text-green-800',
      purple: 'bg-purple-50 border-purple-200 text-purple-800',
      yellow: 'bg-yellow-50 border-yellow-200 text-yellow-800'
    };
    return colorMap[color] || 'bg-gray-50 border-gray-200 text-gray-800';
  }

  // Get progress bar color for ViaIS categories
  static getViaIsCategoryProgressBarColor(color) {
    const colorMap = {
      blue: 'bg-blue-500',
      red: 'bg-red-500',
      pink: 'bg-pink-500',
      green: 'bg-green-500',
      purple: 'bg-purple-500',
      yellow: 'bg-yellow-500'
    };
    return colorMap[color] || 'bg-gray-500';
  }

  // Calculate ViaIS category averages
  static calculateViaIsCategoryAverages(viaIsData) {
    const categoryAverages = {};
    
    Object.entries(VIA_IS_CATEGORIES).forEach(([key, category]) => {
      const scores = category.strengths.map(strength => viaIsData[strength] || 0);
      categoryAverages[key] = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    });

    return categoryAverages;
  }
}
