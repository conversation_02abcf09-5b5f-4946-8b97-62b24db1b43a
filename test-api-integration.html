<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>API Integration Test</h1>
    
    <div class="test-section info">
        <h3>Test Configuration</h3>
        <p><strong>API Base URL:</strong> https://api.chhrone.web.id</p>
        <p><strong>WebSocket URL:</strong> http://localhost:3000</p>
        <p>Make sure you have a valid authentication token in localStorage.</p>
    </div>

    <div class="test-section">
        <h3>1. Test Assessment Service</h3>
        <button onclick="testAssessmentService()">Test Assessment Service</button>
        <div id="assessment-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test WebSocket Connection</h3>
        <button onclick="testWebSocket()">Test WebSocket</button>
        <div id="websocket-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Sample Assessment Submission</h3>
        <button onclick="testSampleSubmission()">Submit Sample Assessment</button>
        <div id="submission-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Test Persona Profile Display</h3>
        <button onclick="testPersonaDisplay()">Test Persona Display</button>
        <div id="persona-result"></div>
    </div>

    <script type="module">
        import { assessmentService } from './src/services/assessmentService.js';
        import { websocketService } from './src/services/websocketService.js';

        window.testAssessmentService = async function() {
            const resultDiv = document.getElementById('assessment-result');
            resultDiv.innerHTML = '<p>Testing assessment service...</p>';
            
            try {
                // Test if service is properly initialized
                const hasToken = !!localStorage.getItem('userToken');
                const hasUnsubmitted = assessmentService.hasUnsubmittedAssessments();
                const isProcessing = assessmentService.isAssessmentProcessing();
                const currentJobId = assessmentService.getCurrentJobId();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>Assessment Service Status:</h4>
                        <ul>
                            <li>Has Auth Token: ${hasToken}</li>
                            <li>Has Unsubmitted Assessments: ${hasUnsubmitted}</li>
                            <li>Is Processing: ${isProcessing}</li>
                            <li>Current Job ID: ${currentJobId || 'None'}</li>
                        </ul>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><strong>Error:</strong> ${error.message}</div>`;
            }
        };

        window.testWebSocket = async function() {
            const resultDiv = document.getElementById('websocket-result');
            resultDiv.innerHTML = '<p>Testing WebSocket connection...</p>';
            
            try {
                await websocketService.connect();
                const status = websocketService.getStatus();
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>WebSocket Status:</h4>
                        <ul>
                            <li>Connected: ${status.connected}</li>
                            <li>Reconnect Attempts: ${status.reconnectAttempts}</li>
                            <li>Max Attempts: ${status.maxReconnectAttempts}</li>
                        </ul>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><strong>WebSocket Error:</strong> ${error.message}</div>`;
            }
        };

        window.testSampleSubmission = async function() {
            const resultDiv = document.getElementById('submission-result');
            resultDiv.innerHTML = '<p>Testing sample assessment submission...</p>';
            
            try {
                // Create sample assessment data
                const sampleData = {
                    riasec: {
                        realistic: 15,
                        investigative: 25,
                        artistic: 20,
                        social: 18,
                        enterprising: 12,
                        conventional: 10
                    },
                    ocean: {
                        openness: 85,
                        conscientiousness: 75,
                        extraversion: 60,
                        agreeableness: 70,
                        neuroticism: 40
                    },
                    viaIs: {
                        creativity: 90,
                        curiosity: 85,
                        judgment: 80,
                        love_of_learning: 88,
                        perspective: 75,
                        bravery: 65,
                        perseverance: 70,
                        honesty: 85,
                        zest: 60,
                        love: 75,
                        kindness: 80,
                        social_intelligence: 70,
                        teamwork: 65,
                        fairness: 85,
                        leadership: 60,
                        forgiveness: 70,
                        humility: 75,
                        prudence: 80,
                        self_regulation: 75,
                        appreciation_of_beauty: 85,
                        gratitude: 80,
                        hope: 75,
                        humor: 70,
                        spirituality: 45
                    }
                };

                const result = await assessmentService.submitAssessment(sampleData);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>Submission Result:</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><strong>Submission Error:</strong> ${error.message}</div>`;
            }
        };

        window.testPersonaDisplay = function() {
            const resultDiv = document.getElementById('persona-result');
            
            try {
                // Test persona profile storage and retrieval
                const samplePersona = {
                    archetype: "The Analytical Innovator",
                    shortSummary: "Test persona profile for API integration",
                    strengthSummary: "Strong analytical and creative abilities",
                    weaknessSummary: "May overthink and prefer working alone",
                    careerRecommendation: [
                        {
                            careerName: "Data Scientist",
                            careerProspect: {
                                jobAvailability: "high",
                                salaryPotential: "high",
                                careerProgression: "high",
                                industryGrowth: "super high"
                            }
                        }
                    ],
                    insights: [
                        "You thrive in analytical environments",
                        "Innovation drives your work satisfaction"
                    ],
                    skillSuggestion: [
                        "Data Analysis",
                        "Critical Thinking",
                        "Research Methods"
                    ],
                    possiblePitfalls: [
                        "Analysis Paralysis - May overthink decisions",
                        "Perfectionism - High standards may slow progress"
                    ],
                    roleModel: [
                        "Marie Curie - Pioneering scientist known for analytical rigor",
                        "Steve Jobs - Visionary leader combining analysis with innovation"
                    ],
                    workEnvironment: "Environment that provides intellectual autonomy and continuous challenges",
                    riskTolerance: "moderate"
                };

                // Store in localStorage
                localStorage.setItem('personaProfile', JSON.stringify(samplePersona));
                localStorage.setItem('assessmentResultReady', 'true');
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>Persona Profile Test:</h4>
                        <p>Sample persona profile stored in localStorage.</p>
                        <p>You can now navigate to the result pages to see the new format in action.</p>
                        <button onclick="window.open('./src/pages/result-persona.js', '_blank')">View Persona Page Code</button>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><strong>Persona Test Error:</strong> ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
