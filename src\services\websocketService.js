// WebSocket Service for real-time notifications
// Handles Socket.IO connection for assessment status updates

export class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.eventHandlers = new Map();
    
    // Use API Gateway URL as recommended
    this.socketURL = 'http://localhost:3000';
  }

  // Connect to WebSocket server
  async connect() {
    try {
      // Check if Socket.IO is available
      if (typeof io === 'undefined') {
        console.warn('Socket.IO not loaded, attempting to load...');
        await this.loadSocketIO();
      }

      // Get authentication token
      const token = localStorage.getItem('userToken');
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Create socket connection
      this.socket = io(this.socketURL, {
        autoConnect: false,
        timeout: 10000,
        transports: ['websocket', 'polling']
      });

      // Set up event listeners
      this.setupEventListeners();

      // Connect and authenticate
      this.socket.connect();
      
      // Authenticate after connection
      this.socket.on('connect', () => {
        console.log('WebSocket connected, authenticating...');
        this.socket.emit('authenticate', { token });
      });

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Authentication timeout'));
        }, 10000);

        this.socket.on('authenticated', () => {
          clearTimeout(timeout);
          this.isConnected = true;
          this.reconnectAttempts = 0;
          console.log('WebSocket authenticated successfully');
          resolve();
        });

        this.socket.on('auth_error', (error) => {
          clearTimeout(timeout);
          console.error('WebSocket authentication failed:', error);
          reject(new Error(error.message || 'Authentication failed'));
        });
      });

    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      throw error;
    }
  }

  // Load Socket.IO library dynamically
  async loadSocketIO() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdn.socket.io/4.7.2/socket.io.min.js';
      script.onload = resolve;
      script.onerror = () => reject(new Error('Failed to load Socket.IO'));
      document.head.appendChild(script);
    });
  }

  // Set up event listeners
  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnected = false;
      
      // Attempt to reconnect if not manually disconnected
      if (reason !== 'io client disconnect') {
        this.attemptReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.attemptReconnect();
    });

    // Assessment-specific events
    this.socket.on('analysis-started', (data) => {
      console.log('Analysis started:', data);
      this.emit('analysis-started', data);
    });

    this.socket.on('analysis-complete', (data) => {
      console.log('Analysis complete:', data);
      this.emit('analysis-complete', data);
    });

    this.socket.on('analysis-failed', (data) => {
      console.error('Analysis failed:', data);
      this.emit('analysis-failed', data);
    });
  }

  // Attempt to reconnect
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      if (this.socket) {
        this.socket.connect();
      }
    }, delay);
  }

  // Add event listener
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event).push(handler);
  }

  // Remove event listener
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      const handlers = this.eventHandlers.get(event);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Emit event to handlers
  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('Error in event handler:', error);
        }
      });
    }
  }

  // Disconnect from WebSocket
  disconnect() {
    if (this.socket) {
      this.isConnected = false;
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventHandlers.clear();
  }

  // Check if connected
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }

  // Get connection status
  getStatus() {
    return {
      connected: this.isSocketConnected(),
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }
}

// Create singleton instance
export const websocketService = new WebSocketService();
