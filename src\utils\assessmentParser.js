// Assessment Questions Parser
// Parses questions from the assessment files and structures them for the 3-phase assessment

export class AssessmentParser {
  constructor() {
    this.phases = {
      1: {
        name: "Big Five Personality",
        description: "Mengukur lima dimensi utama kepribadian Anda",
        file: "Big Five Inventory (BFI-44) Self-Assessment.txt",
        categories: ["Openness to Experience", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"]
      },
      2: {
        name: "RIASEC Holland Codes", 
        description: "Mengidentifikasi minat karir dan lingkungan kerja yang sesuai",
        file: "RIASEC Holland Codes Self-Assessment.txt",
        categories: ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"]
      },
      3: {
        name: "VIA Character Strengths",
        description: "Mengenali kekuatan karakter dan nilai-nilai inti Anda",
        file: "VIA Character Strengths Self-Assessment.txt", 
        categories: ["Wisdom", "Courage", "Humanity", "Justice", "Temperance", "Transcendence"]
      }
    };
  }

  // Parse Big Five questions
  parseBigFiveQuestions() {
    const questions = [];
    let questionId = 1;

    // Openness to Experience
    const opennessQuestions = [
      "Is original, comes up with new ideas",
      "Is curious about many different things", 
      "Is ingenious, a deep thinker",
      "Has an active imagination",
      "Is inventive",
      "Values artistic, aesthetic experiences",
      "Prefers work that is routine", // (R)
      "Likes to reflect, play with ideas",
      "Has few artistic interests", // (R)
      "Is sophisticated in art, music, or literature"
    ];

    opennessQuestions.forEach((text, index) => {
      const isReverse = text.includes("routine") || text.includes("few artistic");
      questions.push({
        id: questionId++,
        phase: 1,
        category: "Openness to Experience",
        text: `Saya adalah seseorang yang ${text.toLowerCase()}`,
        isReverse: isReverse,
        originalText: text
      });
    });

    // Conscientiousness
    const conscientiousnessQuestions = [
      "Does a thorough job",
      "Can be somewhat careless", // (R)
      "Is a reliable worker", 
      "Tends to be disorganized", // (R)
      "Tends to be lazy", // (R)
      "Perseveres until the task is finished",
      "Does things efficiently",
      "Makes plans and follows through with them",
      "Is easily distracted" // (R)
    ];

    conscientiousnessQuestions.forEach((text, index) => {
      const isReverse = text.includes("careless") || text.includes("disorganized") || 
                       text.includes("lazy") || text.includes("distracted");
      questions.push({
        id: questionId++,
        phase: 1,
        category: "Conscientiousness",
        text: `Saya adalah seseorang yang ${text.toLowerCase()}`,
        isReverse: isReverse,
        originalText: text
      });
    });

    // Extraversion
    const extraversionQuestions = [
      "Is talkative",
      "Is reserved", // (R)
      "Is full of energy",
      "Generates a lot of enthusiasm", 
      "Tends to be quiet", // (R)
      "Has an assertive personality",
      "Is sometimes shy, inhibited", // (R)
      "Is outgoing, sociable"
    ];

    extraversionQuestions.forEach((text, index) => {
      const isReverse = text.includes("reserved") || text.includes("quiet") || text.includes("shy");
      questions.push({
        id: questionId++,
        phase: 1,
        category: "Extraversion",
        text: `Saya adalah seseorang yang ${text.toLowerCase()}`,
        isReverse: isReverse,
        originalText: text
      });
    });

    // Agreeableness
    const agreeablenessQuestions = [
      "Tends to find fault with others", // (R)
      "Is helpful and unselfish with others",
      "Starts quarrels with others", // (R)
      "Has a forgiving nature",
      "Is generally trusting",
      "Can be cold and aloof", // (R)
      "Is considerate and kind to almost everyone",
      "Is sometimes rude to others", // (R)
      "Likes to cooperate with others"
    ];

    agreeablenessQuestions.forEach((text, index) => {
      const isReverse = text.includes("fault") || text.includes("quarrels") || 
                       text.includes("cold") || text.includes("rude");
      questions.push({
        id: questionId++,
        phase: 1,
        category: "Agreeableness", 
        text: `Saya adalah seseorang yang ${text.toLowerCase()}`,
        isReverse: isReverse,
        originalText: text
      });
    });

    // Neuroticism
    const neuroticismQuestions = [
      "Is depressed, blue",
      "Is relaxed, handles stress well", // (R)
      "Can be tense",
      "Worries a lot",
      "Is emotionally stable, not easily upset", // (R)
      "Can be moody",
      "Remains calm in tense situations", // (R)
      "Gets nervous easily"
    ];

    neuroticismQuestions.forEach((text, index) => {
      const isReverse = text.includes("relaxed") || text.includes("stable") || text.includes("calm");
      questions.push({
        id: questionId++,
        phase: 1,
        category: "Neuroticism",
        text: `Saya adalah seseorang yang ${text.toLowerCase()}`,
        isReverse: isReverse,
        originalText: text
      });
    });

    return questions;
  }

  // Parse RIASEC questions
  parseRiasecQuestions() {
    const questions = [];
    let questionId = 1000; // Start from 1000 to avoid conflicts with Phase 1

    // Realistic
    const realisticQuestions = [
      "I enjoy working with my hands, using tools, or operating machinery",
      "Building, repairing, or fixing things is satisfying to me",
      "I prefer working outdoors rather than in an office",
      "I am a practical person who likes to see the tangible results of my work",
      "I am good at mechanical tasks and understanding how things work",
      "I like activities such as gardening, construction, or working on cars",
      "I prefer concrete problems over abstract ones",
      "I would rather work with things than with people or ideas",
      "I am adventurous and enjoy physical challenges",
      "I learn best by doing"
    ];

    realisticQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 2,
        category: "Realistic",
        text: text,
        originalText: text
      });
    });

    // Investigative
    const investigativeQuestions = [
      "I am curious about the physical and natural world",
      "I enjoy solving complex problems that require a lot of thought",
      "I like to conduct research, analyze data, and understand theories",
      "I am good at math and science",
      "I enjoy activities like reading scientific journals, doing puzzles, or performing experiments",
      "I am an analytical and logical person",
      "I prefer to work independently and pursue my own ideas",
      "I value precision and accuracy in my work",
      "I would rather work with ideas than with people or things",
      "I am driven to understand why things happen"
    ];

    investigativeQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 2,
        category: "Investigative",
        text: text,
        originalText: text
      });
    });

    // Artistic
    const artisticQuestions = [
      "I have a good imagination and enjoy expressing myself in creative ways",
      "I like to work in unstructured environments where I can be original",
      "I enjoy activities like painting, writing, playing music, or dancing",
      "I am an expressive, original, and independent person",
      "I appreciate art, music, and literature",
      "I would rather work with ideas and things than with people",
      "I am not a fan of rules or strict routines",
      "I see the world through a creative and unconventional lens",
      "I am good at coming up with new ideas",
      "Self-expression is very important to me"
    ];

    artisticQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 2,
        category: "Artistic",
        text: text,
        originalText: text
      });
    });

    // Social
    const socialQuestions = [
      "I enjoy helping, teaching, or counseling other people",
      "I am good at understanding and empathizing with others' feelings",
      "I like to work in groups and collaborate with others",
      "I am a friendly, helpful, and cooperative person",
      "I value relationships and making a difference in my community",
      "I would rather work with people than with things or ideas",
      "I am a good listener and people often come to me with their problems",
      "I enjoy activities like volunteering, mentoring, or customer service",
      "I am skilled at communicating and resolving conflicts",
      "Making people feel comfortable and supported is important to me"
    ];

    socialQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 2,
        category: "Social",
        text: text,
        originalText: text
      });
    });

    // Enterprising
    const enterprisingQuestions = [
      "I enjoy leading, persuading, and motivating others",
      "I am ambitious and enjoy taking on challenges for a high reward",
      "I am good at public speaking and selling ideas or products",
      "I am an energetic, confident, and assertive person",
      "I like to work in positions of influence and authority",
      "I would rather work with people and data than with things",
      "I am interested in business, politics, and finance",
      "I enjoy taking risks and making decisions",
      "I am good at organizing and managing projects or people",
      "I am competitive and like to win"
    ];

    enterprisingQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 2,
        category: "Enterprising",
        text: text,
        originalText: text
      });
    });

    // Conventional
    const conventionalQuestions = [
      "I enjoy working with data, numbers, and details",
      "I like to work in structured environments with clear rules and procedures",
      "I am good at organizing information, keeping records, and following a plan",
      "I am a careful, orderly, and responsible person",
      "I value accuracy and efficiency",
      "I would rather work with data and things than with ideas",
      "I am good at tasks that require precision and attention to detail",
      "I prefer to have clear instructions and know exactly what is expected of me",
      "I enjoy activities like accounting, programming, or managing databases",
      "I am dependable and take my responsibilities seriously"
    ];

    conventionalQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 2,
        category: "Conventional",
        text: text,
        originalText: text
      });
    });

    return questions;
  }

  // Parse VIA Character Strengths questions
  parseViaQuestions() {
    const questions = [];
    let questionId = 2000; // Start from 2000 to avoid conflicts with Phase 1 & 2

    // Wisdom
    const wisdomQuestions = [
      "I am always coming up with new ways to do things",
      "Thinking of novel ideas is one of my greatest strengths",
      "I am an original thinker",
      "When I have a problem, I enjoy finding a creative solution",
      "I am always exploring the world and asking 'why?'",
      "I find many things fascinating",
      "I am easily bored by things I have already experienced",
      "I love to visit new places and learn about them",
      "I always weigh the pros and cons before making a decision",
      "My friends come to me for advice because I see things clearly and without bias",
      "I make sure to have all the facts before I form an opinion",
      "Thinking things through is a critical part of who I am",
      "I am thrilled when I can learn something new",
      "I enjoy the challenge of mastering a difficult subject",
      "I am a lifelong learner",
      "I often read books or articles to improve my knowledge",
      "People describe me as wise",
      "I have a knack for seeing the big picture in complex situations",
      "I am able to look at things from many different angles, which helps me give good advice",
      "I can often find a way to make sense of the world when others are confused"
    ];

    wisdomQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 3,
        category: "Wisdom",
        text: text,
        originalText: text
      });
    });

    // Courage
    const courageQuestions = [
      "I am a courageous person, even when I feel scared",
      "I always stand up for what is right, even if it means facing opposition",
      "I do not back down from a threat or challenge",
      "Facing my fears makes me feel stronger",
      "I never give up on a goal once I've started",
      "I am a hard worker and finish what I begin",
      "Setbacks don't discourage me; they make me work harder",
      "I am diligent and disciplined in my work",
      "I live my life in a genuine and authentic way",
      "Being truthful is more important to me than being popular",
      "I am a down-to-earth person; I don't pretend to be something I'm not",
      "My friends and family know they can count on me to be straightforward",
      "I wake up excited to start the day",
      "I approach everything I do with energy and enthusiasm",
      "I feel alive and full of vitality",
      "I want to live life to the fullest"
    ];

    courageQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 3,
        category: "Courage",
        text: text,
        originalText: text
      });
    });

    // Humanity
    const humanityQuestions = [
      "I feel a deep sense of connection to the people I am close to",
      "I am at my best when I am sharing my life with others",
      "It is easy for me to express love and warmth to others",
      "I value my close relationships above all else",
      "I love to do good deeds for others, even if they are strangers",
      "I am a generous and caring person",
      "Helping other people is one of the most important things to me",
      "I enjoy taking care of people",
      "I am skilled at understanding what makes other people tick",
      "I am always aware of my own feelings and the feelings of those around me",
      "I know what to do to make other people feel at ease",
      "I can easily adapt my behavior to fit in with different groups of people"
    ];

    humanityQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 3,
        category: "Humanity",
        text: text,
        originalText: text
      });
    });

    // Justice
    const justiceQuestions = [
      "I am a loyal and dedicated member of any group I join",
      "I work best when I am collaborating with others",
      "I always do my share of the work in a group project",
      "I feel a strong sense of duty to the teams I belong to",
      "I believe in giving everyone a fair chance",
      "I treat all people equally, regardless of their background",
      "I cannot stand to see people treated unjustly",
      "My decisions are guided by a strong sense of justice",
      "I am good at organizing groups to get things done",
      "People look to me to lead the way",
      "I enjoy planning and directing activities for a group",
      "I make sure everyone in my group feels included and valued"
    ];

    justiceQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 3,
        category: "Justice",
        text: text,
        originalText: text
      });
    });

    // Temperance
    const temperanceQuestions = [
      "I am a forgiving person; I don't hold grudges",
      "I always try to give people a second chance",
      "I believe in mercy and letting go of anger",
      "When someone hurts me, I try to understand their side of the story",
      "I prefer to let my actions speak for themselves",
      "I do not seek the spotlight or need to be the center of attention",
      "I am aware of my limitations and don't overstate my accomplishments",
      "People would say I am a humble person",
      "I am a very careful person",
      "I avoid doing things I might regret later",
      "I think about the consequences before I act",
      "I am not a risk-taker",
      "I am a disciplined person and can control my desires",
      "I am good at resisting temptations",
      "I can keep my emotions in check",
      "I am in control of my actions"
    ];

    temperanceQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 3,
        category: "Temperance",
        text: text,
        originalText: text
      });
    });

    // Transcendence
    const transcendenceQuestions = [
      "I often feel a sense of awe when I see something beautiful in nature",
      "I deeply appreciate art, music, and skilled performances",
      "I notice the beauty all around me every day",
      "Witnessing an act of excellence gives me a thrill",
      "I often stop to count my blessings",
      "I feel a deep sense of thankfulness for the good things in my life",
      "I make a point to express my thanks to others",
      "I am an aware and grateful person",
      "I am optimistic about my future",
      "I believe that good things are going to happen",
      "I work hard to achieve my goals because I expect to succeed",
      "Even when things are tough, I stay focused on a positive outcome",
      "I love to laugh and make other people laugh",
      "I have a playful side and enjoy joking around",
      "I can always find the light side of a difficult situation",
      "Bringing a smile to someone's face makes my day",
      "My faith or beliefs give my life meaning and purpose",
      "I feel a strong connection to a higher power or the universe as a whole",
      "My beliefs shape my actions and who I am",
      "I have a sense of purpose that transcends my day-to-day life"
    ];

    transcendenceQuestions.forEach((text, index) => {
      questions.push({
        id: questionId++,
        phase: 3,
        category: "Transcendence",
        text: text,
        originalText: text
      });
    });

    return questions;
  }

  // Get all questions for a specific phase
  getPhaseQuestions(phaseNumber) {
    switch(phaseNumber) {
      case 1:
        return this.parseBigFiveQuestions();
      case 2:
        return this.parseRiasecQuestions();
      case 3:
        return this.parseViaQuestions();
      default:
        return [];
    }
  }

  // Get phase information
  getPhaseInfo(phaseNumber) {
    return this.phases[phaseNumber] || null;
  }

  // Get all phases
  getAllPhases() {
    return this.phases;
  }

  // Create Likert scale options (1-7)
  getLikertScale() {
    return [
      { value: 1, label: "Sangat Tidak Setuju", shortLabel: "1" },
      { value: 2, label: "Tidak Setuju", shortLabel: "2" },
      { value: 3, label: "Agak Tidak Setuju", shortLabel: "3" },
      { value: 4, label: "Netral", shortLabel: "4" },
      { value: 5, label: "Agak Setuju", shortLabel: "5" },
      { value: 6, label: "Setuju", shortLabel: "6" },
      { value: 7, label: "Sangat Setuju", shortLabel: "7" }
    ];
  }

  // Get question mapping for scoring
  getQuestionMapping() {
    return {
      // Big Five (Phase 1) - Question ID ranges for each category
      bigFive: {
        openness: [1, 6, 11, 16, 21, 26, 31, 36, 41, 44], // 10 questions
        conscientiousness: [2, 7, 12, 17, 22, 27, 32, 37, 42], // 9 questions
        extraversion: [3, 8, 13, 18, 23, 28, 33, 38], // 8 questions
        agreeableness: [4, 9, 14, 19, 24, 29, 34, 39, 43], // 9 questions
        neuroticism: [5, 10, 15, 20, 25, 30, 35, 40] // 8 questions
      },
      // RIASEC (Phase 2) - Question ID ranges for each category
      riasec: {
        realistic: [1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009],
        investigative: [1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019],
        artistic: [1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029],
        social: [1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039],
        enterprising: [1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049],
        conventional: [1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059]
      },
      // VIA Character Strengths (Phase 3) - 24 strengths, 4 questions each
      viaIs: {
        creativity: [2000, 2001, 2002, 2003],
        curiosity: [2004, 2005, 2006, 2007],
        judgment: [2008, 2009, 2010, 2011],
        loveOfLearning: [2012, 2013, 2014, 2015],
        perspective: [2016, 2017, 2018, 2019],
        bravery: [2020, 2021, 2022, 2023],
        perseverance: [2024, 2025, 2026, 2027],
        honesty: [2028, 2029, 2030, 2031],
        zest: [2032, 2033, 2034, 2035],
        love: [2036, 2037, 2038, 2039],
        kindness: [2040, 2041, 2042, 2043],
        socialIntelligence: [2044, 2045, 2046, 2047],
        teamwork: [2048, 2049, 2050, 2051],
        fairness: [2052, 2053, 2054, 2055],
        leadership: [2056, 2057, 2058, 2059],
        forgiveness: [2060, 2061, 2062, 2063],
        humility: [2064, 2065, 2066, 2067],
        prudence: [2068, 2069, 2070, 2071],
        selfRegulation: [2072, 2073, 2074, 2075],
        appreciationOfBeauty: [2076, 2077, 2078, 2079],
        gratitude: [2080, 2081, 2082, 2083],
        hope: [2084, 2085, 2086, 2087],
        humor: [2088, 2089, 2090, 2091],
        spirituality: [2092, 2093, 2094, 2095]
      }
    };
  }

  // Get reverse scored questions for Big Five
  getReverseQuestions() {
    // These are the question IDs that need reverse scoring for Big Five
    return [
      // Based on the parsing logic in parseBigFiveQuestions
      7, 12, 17, 22, 27, 32, 37, 42, // Conscientiousness reverse items
      8, 13, 23, 28, 33, // Extraversion reverse items
      4, 9, 14, 24, 29, 34, 39, // Agreeableness reverse items
      10, 15, 25, 30, 35 // Neuroticism reverse items (relaxed, stable, calm)
    ];
  }
}

// Create singleton instance
export const assessmentParser = new AssessmentParser();
