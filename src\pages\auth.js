export function createAuthPage() {
  return `
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 id="auth-title" class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Masuk ke Akun Anda
        </h2>
        <p id="auth-subtitle" class="mt-2 text-center text-sm text-gray-600">
          Atau
          <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500" onclick="toggleAuthMode()">
            daftar akun baru
          </a>
        </p>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <!-- Error/Success Messages -->
          <div id="auth-message" class="hidden mb-4 p-3 rounded-md">
            <p id="auth-message-text" class="text-sm"></p>
          </div>

          <!-- Login Form -->
          <form id="login-form" class="space-y-6" onsubmit="handleLogin(event)">
            <div>
              <label for="login-email" class="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div class="mt-1">
                <input id="login-email" name="email" type="email" autocomplete="email" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              </div>
            </div>

            <div>
              <label for="login-password" class="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div class="mt-1">
                <input id="login-password" name="password" type="password" autocomplete="current-password" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input id="remember-me" name="remember-me" type="checkbox"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                  Ingat saya
                </label>
              </div>

              <div class="text-sm">
                <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                  Lupa password?
                </a>
              </div>
            </div>

            <div>
              <button type="submit" id="login-button"
                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <span id="login-button-text">Masuk</span>
                <svg id="login-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </button>
            </div>
          </form>

          <!-- Register Form -->
          <form id="register-form" class="space-y-6 hidden" onsubmit="handleRegister(event)">
            <div>
              <label for="register-email" class="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div class="mt-1">
                <input id="register-email" name="email" type="email" autocomplete="email" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              </div>
              <p class="mt-1 text-xs text-gray-500">Maksimal 255 karakter</p>
            </div>

            <div>
              <label for="register-password" class="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div class="mt-1">
                <input id="register-password" name="password" type="password" autocomplete="new-password" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              </div>
              <p class="mt-1 text-xs text-gray-500">Minimal 8 karakter, harus mengandung huruf dan angka</p>
            </div>

            <div>
              <label for="confirm-password" class="block text-sm font-medium text-gray-700">
                Konfirmasi Password
              </label>
              <div class="mt-1">
                <input id="confirm-password" name="confirmPassword" type="password" autocomplete="new-password" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              </div>
            </div>

            <div>
              <button type="submit" id="register-button"
                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <span id="register-button-text">Daftar</span>
                <svg id="register-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `;
}

// Import auth service
import { authService } from '../services/authService.js';

let isLoginMode = true;

// Toggle between login and register forms
export function toggleAuthMode() {
  isLoginMode = !isLoginMode;

  const loginForm = document.getElementById('login-form');
  const registerForm = document.getElementById('register-form');
  const title = document.getElementById('auth-title');
  const subtitle = document.getElementById('auth-subtitle');

  if (isLoginMode) {
    loginForm.classList.remove('hidden');
    registerForm.classList.add('hidden');
    title.textContent = 'Masuk ke Akun Anda';
    subtitle.innerHTML = `
      Atau
      <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500" onclick="toggleAuthMode()">
        daftar akun baru
      </a>
    `;
  } else {
    loginForm.classList.add('hidden');
    registerForm.classList.remove('hidden');
    title.textContent = 'Daftar Akun Baru';
    subtitle.innerHTML = `
      Sudah punya akun?
      <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500" onclick="toggleAuthMode()">
        masuk di sini
      </a>
    `;
  }

  // Clear any previous messages
  hideMessage();
}

// Show message (error or success)
function showMessage(message, isError = false) {
  const messageDiv = document.getElementById('auth-message');
  const messageText = document.getElementById('auth-message-text');

  messageDiv.className = `mb-4 p-3 rounded-md ${isError ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'}`;
  messageText.className = `text-sm ${isError ? 'text-red-700' : 'text-green-700'}`;
  messageText.textContent = message;
  messageDiv.classList.remove('hidden');
}

// Hide message
function hideMessage() {
  const messageDiv = document.getElementById('auth-message');
  messageDiv.classList.add('hidden');
}

// Show loading state
function setLoadingState(formType, isLoading) {
  const button = document.getElementById(`${formType}-button`);
  const buttonText = document.getElementById(`${formType}-button-text`);
  const spinner = document.getElementById(`${formType}-spinner`);

  if (isLoading) {
    button.disabled = true;
    buttonText.textContent = formType === 'login' ? 'Masuk...' : 'Mendaftar...';
    spinner.classList.remove('hidden');
  } else {
    button.disabled = false;
    buttonText.textContent = formType === 'login' ? 'Masuk' : 'Daftar';
    spinner.classList.add('hidden');
  }
}

// Handle login form submission
export async function handleLogin(event) {
  event.preventDefault();
  hideMessage();
  setLoadingState('login', true);

  const formData = new FormData(event.target);
  const email = formData.get('email');
  const password = formData.get('password');

  try {
    const result = await authService.login(email, password);

    if (result.success) {
      showMessage('Login berhasil! Mengalihkan ke dashboard...', false);

      // Redirect to dashboard after short delay
      setTimeout(() => {
        window.location.hash = '#dashboard';
      }, 1000);
    } else {
      showMessage(result.error || 'Login gagal. Silakan coba lagi.', true);
    }
  } catch (error) {
    showMessage('Terjadi kesalahan. Silakan coba lagi.', true);
    console.error('Login error:', error);
  } finally {
    setLoadingState('login', false);
  }
}

// Handle register form submission
export async function handleRegister(event) {
  event.preventDefault();
  hideMessage();
  setLoadingState('register', true);

  const formData = new FormData(event.target);
  const email = formData.get('email');
  const password = formData.get('password');
  const confirmPassword = formData.get('confirmPassword');

  // Validate password confirmation
  if (password !== confirmPassword) {
    showMessage('Password dan konfirmasi password tidak cocok.', true);
    setLoadingState('register', false);
    return;
  }

  try {
    const result = await authService.register(email, password);

    if (result.success) {
      showMessage('Registrasi berhasil! Mengalihkan ke dashboard...', false);

      // Redirect to dashboard after short delay
      setTimeout(() => {
        window.location.hash = '#dashboard';
      }, 1000);
    } else {
      showMessage(result.error || 'Registrasi gagal. Silakan coba lagi.', true);
    }
  } catch (error) {
    showMessage('Terjadi kesalahan. Silakan coba lagi.', true);
    console.error('Register error:', error);
  } finally {
    setLoadingState('register', false);
  }
}

// Legacy function for backward compatibility
export function showRegister() {
  if (isLoginMode) {
    toggleAuthMode();
  }
}

// Make functions globally available
window.toggleAuthMode = toggleAuthMode;
window.handleLogin = handleLogin;
window.handleRegister = handleRegister;
window.showRegister = showRegister;
