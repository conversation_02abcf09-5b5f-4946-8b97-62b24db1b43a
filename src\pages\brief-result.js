// Modern, futuristic, and premium assessment results page
export function createBriefResultPage() {
  return '' +
    '<div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50">' +
      // Futuristic Navigation
      '<nav class="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/80 border-b border-gray-200/60">' +
        '<div class="max-w-7xl mx-auto px-6 py-4">' +
          '<div class="flex items-center justify-between">' +
            '<div class="flex items-center space-x-8">' +
              '<button onclick="navigateTo(\'dashboard\')" class="group flex items-center text-gray-500 hover:text-gray-900 transition-all duration-300">' +
                '<div class="w-8 h-8 mr-3 rounded-full bg-gray-100 group-hover:bg-gray-200 flex items-center justify-center transition-all duration-300">' +
                  '<svg class="w-4 h-4 group-hover:-translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                  '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>' +
                  '</svg>' +
                '</div>' +
                '<span class="font-medium tracking-wide">Dashboard</span>' +
              '</button>' +
              '<div class="h-4 w-px bg-gray-300"></div>' +
              '<h1 class="text-lg font-semibold text-gray-900 tracking-wide">Assessment Results</h1>' +
            '</div>' +
            '<div class="flex items-center space-x-4">' +
              '<button onclick="downloadResult()" class="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">' +
                '<svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>' +
                '</svg>' +
                'Download Report' +
              '</button>' +
              '<button onclick="shareResult()" class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-all duration-300">' +
                '<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>' +
                '</svg>' +
              '</button>' +
            '</div>' +
          '</div>' +
        '</div>' +
      '</nav>' +

      // Main Content
      '<div class="max-w-6xl mx-auto px-6 pt-24 pb-12">' +
        // Hero Section - Completion
        '<div class="text-center mb-20">' +
          '<div class="relative inline-block mb-8">' +
            '<div class="w-32 h-32 bg-white/70 backdrop-blur-xl rounded-3xl flex items-center justify-center shadow-xl border border-gray-200/60">' +
              '<div class="w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg relative overflow-hidden">' +
                '<div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>' +
                '<svg class="w-10 h-10 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>' +
                '</svg>' +
              '</div>' +
            '</div>' +
            '<div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">' +
              '<div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>' +
            '</div>' +
          '</div>' +
          '<h2 class="text-5xl font-bold text-gray-900 mb-6 tracking-tight">Assessment Complete</h2>' +
          '<p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">' +
            'Congratulations! Your comprehensive psychometric assessment has been completed with precision. ' +
            'Discover your unique potential, strengths, and pathways to excellence.' +
          '</p>' +
          '<div class="inline-flex items-center px-8 py-4 bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/60 shadow-lg">' +
            '<svg class="w-5 h-5 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>' +
            '</svg>' +
            '<span class="text-gray-700 font-medium">Completed: <span id="completion-date" class="text-gray-900 font-semibold"></span></span>' +
          '</div>' +
        '</div>' +

        // Persona Card - Premium Design
        '<div class="bg-white/70 backdrop-blur-xl rounded-3xl p-10 mb-16 border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden">' +
          '<div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 rounded-3xl"></div>' +
          '<div class="relative z-10">' +
            '<div class="flex items-start space-x-8">' +
              '<div class="flex-shrink-0">' +
                '<div class="w-28 h-28 bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">' +
                  '<div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>' +
                  '<svg class="w-12 h-12 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                  '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>' +
                  '</svg>' +
                '</div>' +
              '</div>' +
              '<div class="flex-1">' +
                '<h3 class="text-4xl font-bold text-gray-900 mb-4 tracking-tight" id="archetype">The Analytical Innovator</h3>' +
                '<p class="text-gray-600 text-lg leading-relaxed mb-6" id="short-summary">' +
                  'You are an analytical thinker with strong investigative tendencies and exceptional creativity.' +
                '</p>' +
                '<div class="flex flex-wrap gap-4">' +
                  '<div class="px-5 py-2.5 bg-gradient-to-r from-gray-50 to-gray-100 rounded-full border border-gray-200">' +
                    '<span class="text-sm text-gray-600 font-medium">Risk Tolerance: </span>' +
                    '<span id="risk-tolerance" class="text-sm font-bold text-gray-900">Moderate</span>' +
                  '</div>' +
                  '<div class="px-5 py-2.5 bg-gradient-to-r from-emerald-50 to-green-50 rounded-full border border-emerald-100">' +
                    '<span class="text-sm font-medium text-emerald-700">' +
                      '<svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">' +
                      '<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>' +
                      '</svg>' +
                      'Scientifically Validated' +
                    '</span>' +
                  '</div>' +
                '</div>' +
              '</div>' +
            '</div>' +
          '</div>' +
        '</div>' +

        // Assessment Overview - Premium Grid Layout
        '<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">' +
          // VIA-IS Card - Character Strengths
          '<div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-500 cursor-pointer group" onclick="navigateTo(\'result-via-is\')">' +
            '<div class="flex items-center justify-between mb-6">' +
              '<div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 relative overflow-hidden">' +
                '<div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>' +
                '<svg class="w-8 h-8 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>' +
                '</svg>' +
              '</div>' +
              '<svg class="w-6 h-6 text-gray-400 group-hover:text-purple-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
              '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>' +
              '</svg>' +
            '</div>' +
            '<h4 class="text-xl font-bold text-gray-900 mb-3">Character Strengths</h4>' +
            '<p class="text-gray-600 mb-6 leading-relaxed">24 character strengths across 6 virtue categories for personal excellence</p>' +
            '<div class="space-y-3" id="via-is-preview"></div>' +
            '<div class="mt-6 pt-4 border-t border-gray-200/60">' +
              '<div class="flex items-center text-sm text-purple-600 font-medium">' +
                '<span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>' +
                'VIA-IS Assessment' +
              '</div>' +
            '</div>' +
          '</div>' +

          // RIASEC Card - Career Interests
          '<div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-500 cursor-pointer group" onclick="navigateTo(\'result-riasec\')">' +
            '<div class="flex items-center justify-between mb-6">' +
              '<div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 relative overflow-hidden">' +
                '<div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>' +
                '<svg class="w-8 h-8 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>' +
                '</svg>' +
              '</div>' +
              '<svg class="w-6 h-6 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
              '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>' +
              '</svg>' +
            '</div>' +
            '<h4 class="text-xl font-bold text-gray-900 mb-3">Career Interests</h4>' +
            '<p class="text-gray-600 mb-6 leading-relaxed">Holland Codes for optimal career alignment and professional growth</p>' +
            '<div class="space-y-3" id="riasec-preview"></div>' +
            '<div class="mt-6 pt-4 border-t border-gray-200/60">' +
              '<div class="flex items-center text-sm text-blue-600 font-medium">' +
                '<span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>' +
                'RIASEC Assessment' +
              '</div>' +
            '</div>' +
          '</div>' +

          // OCEAN Card - Personality Traits
          '<div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-500 cursor-pointer group" onclick="navigateTo(\'result-ocean\')">' +
            '<div class="flex items-center justify-between mb-6">' +
              '<div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 relative overflow-hidden">' +
                '<div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>' +
                '<svg class="w-8 h-8 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>' +
                '</svg>' +
              '</div>' +
              '<svg class="w-6 h-6 text-gray-400 group-hover:text-emerald-500 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
              '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>' +
              '</svg>' +
            '</div>' +
            '<h4 class="text-xl font-bold text-gray-900 mb-3">Personality Traits</h4>' +
            '<p class="text-gray-600 mb-6 leading-relaxed">Big Five personality dimensions for comprehensive behavioral insights</p>' +
            '<div class="space-y-3" id="ocean-preview"></div>' +
            '<div class="mt-6 pt-4 border-t border-gray-200/60">' +
              '<div class="flex items-center text-sm text-emerald-600 font-medium">' +
                '<span class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>' +
                'OCEAN Assessment' +
              '</div>' +
            '</div>' +
          '</div>' +
        '</div>' +

        // Progress Journey - Modern Design
        '<div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 mb-12 border border-gray-200/60 shadow-lg">' +
          '<div class="text-center mb-8">' +
            '<h3 class="text-2xl font-bold text-gray-900 mb-2">Explore Your Results</h3>' +
            '<p class="text-gray-600">Navigate through your comprehensive assessment insights</p>' +
          '</div>' +
          '<div class="flex items-center justify-between">' +
            // Step 1 - Active
            '<div class="flex flex-col items-center text-center">' +
              '<div class="w-14 h-14 bg-gradient-to-br from-gray-800 to-gray-900 text-white rounded-2xl flex items-center justify-center font-bold mb-3 shadow-lg relative overflow-hidden">' +
                '<div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>' +
                '<span class="relative z-10">1</span>' +
              '</div>' +
              '<div class="text-sm font-semibold text-gray-900">Overview</div>' +
              '<div class="text-xs text-gray-500 mt-1">Current</div>' +
            '</div>' +
            '<div class="flex-1 h-0.5 bg-gradient-to-r from-gray-300 to-gray-200 mx-6"></div>' +

            // Step 2
            '<div class="flex flex-col items-center text-center cursor-pointer group" onclick="navigateTo(\'result-via-is\')">' +
              '<div class="w-14 h-14 bg-gradient-to-br from-purple-100 to-indigo-100 text-purple-600 rounded-2xl flex items-center justify-center font-bold mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">' +
                '2' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-700 group-hover:text-purple-600 transition-colors">Strengths</div>' +
              '<div class="text-xs text-gray-400 mt-1">VIA-IS</div>' +
            '</div>' +
            '<div class="flex-1 h-0.5 bg-gradient-to-r from-gray-200 to-gray-300 mx-6"></div>' +

            // Step 3
            '<div class="flex flex-col items-center text-center cursor-pointer group" onclick="navigateTo(\'result-riasec\')">' +
              '<div class="w-14 h-14 bg-gradient-to-br from-blue-100 to-cyan-100 text-blue-600 rounded-2xl flex items-center justify-center font-bold mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">' +
                '3' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors">Interests</div>' +
              '<div class="text-xs text-gray-400 mt-1">RIASEC</div>' +
            '</div>' +
            '<div class="flex-1 h-0.5 bg-gradient-to-r from-gray-300 to-gray-200 mx-6"></div>' +

            // Step 4
            '<div class="flex flex-col items-center text-center cursor-pointer group" onclick="navigateTo(\'result-ocean\')">' +
              '<div class="w-14 h-14 bg-gradient-to-br from-emerald-100 to-teal-100 text-emerald-600 rounded-2xl flex items-center justify-center font-bold mb-3 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">' +
                '4' +
              '</div>' +
              '<div class="text-sm font-medium text-gray-700 group-hover:text-emerald-600 transition-colors">Personality</div>' +
              '<div class="text-xs text-gray-400 mt-1">OCEAN</div>' +
            '</div>' +
          '</div>' +

          '<div class="text-center mt-8">' +
            '<p class="text-gray-600 mb-6 max-w-2xl mx-auto leading-relaxed">' +
              'Follow the exploration journey to understand your assessment results in depth. ' +
              'Each stage provides valuable insights for personal and career development.' +
            '</p>' +
            '<button onclick="navigateTo(\'result-via-is\')" ' +
              'class="px-8 py-3 bg-gradient-to-r from-gray-800 to-gray-900 text-white rounded-xl hover:from-gray-900 hover:to-black transition-all duration-300 text-lg font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">' +
              'Start Exploration' +
            '</button>' +
          '</div>' +
        '</div>' +

        // Quick Actions - Modern Grid
        '<div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">' +
          // Quick Access
          '<div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">' +
            '<div class="flex items-center mb-6">' +
              '<div class="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center mr-3 shadow-md">' +
                '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>' +
                '</svg>' +
              '</div>' +
              '<h4 class="text-lg font-bold text-gray-900">Quick Access</h4>' +
            '</div>' +
            '<div class="space-y-4">' +
              '<button onclick="navigateTo(\'result-via-is\')" ' +
                'class="w-full text-left p-4 rounded-xl border border-gray-200/60 hover:border-purple-300 hover:bg-purple-50/50 transition-all duration-300 group backdrop-blur-sm">' +
                '<div class="font-semibold text-gray-900 group-hover:text-purple-900">Character Strengths</div>' +
                '<div class="text-sm text-gray-600 group-hover:text-purple-700 mt-1">Explore your 24 character strengths</div>' +
              '</button>' +
              '<button onclick="navigateTo(\'result-riasec\')" ' +
                'class="w-full text-left p-4 rounded-xl border border-gray-200/60 hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-300 group backdrop-blur-sm">' +
                '<div class="font-semibold text-gray-900 group-hover:text-blue-900">Career Interests</div>' +
                '<div class="text-sm text-gray-600 group-hover:text-blue-700 mt-1">Discover your work personality type</div>' +
              '</button>' +
              '<button onclick="navigateTo(\'result-ocean\')" ' +
                'class="w-full text-left p-4 rounded-xl border border-gray-200/60 hover:border-emerald-300 hover:bg-emerald-50/50 transition-all duration-300 group backdrop-blur-sm">' +
                '<div class="font-semibold text-gray-900 group-hover:text-emerald-900">Personality Traits</div>' +
                '<div class="text-sm text-gray-600 group-hover:text-emerald-700 mt-1">Understand your 5 personality dimensions</div>' +
              '</button>' +
            '</div>' +
          '</div>' +

          // Next Steps
          '<div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">' +
            '<div class="flex items-center mb-6">' +
              '<div class="w-10 h-10 bg-gradient-to-br from-emerald-600 to-green-700 rounded-xl flex items-center justify-center mr-3 shadow-md">' +
                '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>' +
                '</svg>' +
              '</div>' +
              '<h4 class="text-lg font-bold text-gray-900">Next Steps</h4>' +
            '</div>' +
            '<div class="space-y-6">' +
              '<div class="flex items-start space-x-4">' +
                '<div class="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-emerald-100 to-green-100 text-emerald-700 rounded-xl flex items-center justify-center text-sm font-bold shadow-sm">' +
                  '1' +
                '</div>' +
                '<div>' +
                  '<div class="font-semibold text-gray-900">Explore Detailed Results</div>' +
                  '<div class="text-sm text-gray-600 mt-1 leading-relaxed">Follow the exploration journey for deep insights</div>' +
                '</div>' +
              '</div>' +
              '<div class="flex items-start space-x-4">' +
                '<div class="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-emerald-100 to-green-100 text-emerald-700 rounded-xl flex items-center justify-center text-sm font-bold shadow-sm">' +
                  '2' +
                '</div>' +
                '<div>' +
                  '<div class="font-semibold text-gray-900">Create Development Plan</div>' +
                  '<div class="text-sm text-gray-600 mt-1 leading-relaxed">Use insights to plan your personal development</div>' +
                '</div>' +
              '</div>' +
              '<div class="flex items-start space-x-4">' +
                '<div class="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-emerald-100 to-green-100 text-emerald-700 rounded-xl flex items-center justify-center text-sm font-bold shadow-sm">' +
                  '3' +
                '</div>' +
                '<div>' +
                  '<div class="font-semibold text-gray-900">Expert Consultation</div>' +
                  '<div class="text-sm text-gray-600 mt-1 leading-relaxed">Discuss results with career counselor</div>' +
                '</div>' +
              '</div>' +
            '</div>' +
            '<button onclick="scheduleConsultation()" ' +
              'class="w-full mt-8 px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-xl hover:from-emerald-700 hover:to-green-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">' +
              'Schedule Consultation' +
            '</button>' +
          '</div>' +
        '</div>' +

        // Action Buttons - Bottom
        '<div class="flex flex-col sm:flex-row gap-6">' +
          '<button onclick="retakeAssessment()" ' +
            'class="flex-1 px-8 py-4 bg-white/70 backdrop-blur-xl border border-gray-200/60 text-gray-700 rounded-xl hover:bg-gray-50/70 transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">' +
            'Retake Assessment' +
          '</button>' +
          '<button onclick="navigateTo(\'result-via-is\')" ' +
            'class="flex-1 px-8 py-4 bg-gradient-to-r from-gray-800 to-gray-900 text-white rounded-xl hover:from-gray-900 hover:to-black transition-all duration-300 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">' +
            'Start Exploration' +
          '</button>' +
        '</div>' +
      '</div>' +
    '</div>';
}

// Initialization function (unchanged)
export function initBriefResult() {
  const completionDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const dateElement = document.getElementById('completion-date');
  if (dateElement) {
    dateElement.textContent = completionDate;
  }
  loadBriefResults();
}

// Load and display functions (unchanged functionality, updated styling)
function loadBriefResults() {
  try {
    const savedProfile = localStorage.getItem('personaProfile');
    const profile = savedProfile ? JSON.parse(savedProfile) : getPersonaProfileExample();
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleAssessmentResults();
    console.log('Brief results loaded:', { profile, assessmentResults });
    displayBriefResults(profile, assessmentResults);
  } catch (error) {
    console.error('Error loading brief results:', error);
    displayBriefResults(getPersonaProfileExample(), getSampleAssessmentResults());
  }
}

function displayBriefResults(profile, assessmentResults) {
  const archetypeElement = document.getElementById('archetype');
  const shortSummaryElement = document.getElementById('short-summary');
  const riskToleranceElement = document.getElementById('risk-tolerance');
  
  if (archetypeElement) archetypeElement.textContent = profile.archetype;
  if (shortSummaryElement) shortSummaryElement.textContent = profile.shortSummary;
  if (riskToleranceElement) riskToleranceElement.textContent = profile.riskTolerance;
  
  displayViaIsPreview(assessmentResults.viaIs);
  displayRiasecPreview(assessmentResults.riasec);
  displayOceanPreview(assessmentResults.ocean);
}

function displayViaIsPreview(viaIsData) {
  const container = document.getElementById('via-is-preview');
  if (!container) return;
  
  const topStrengths = Object.entries(viaIsData)
    .map(function(entry) {
      var key = entry[0];
      var value = entry[1];
      return { key: key, value: value, name: getViaIsDisplayName(key) };
    })
    .sort(function(a, b) { return b.value - a.value; })
    .slice(0, 3);
  
  var html = '';
  for (var i = 0; i < topStrengths.length; i++) {
    var strength = topStrengths[i];
    html += '<div class="flex justify-between items-center py-2">' +
      '<span class="text-sm text-gray-700 font-medium">' + strength.name + '</span>' +
      '<span class="text-sm font-semibold text-purple-600">' + strength.value + '%</span>' +
      '</div>';
  }
  container.innerHTML = html;
}

function displayRiasecPreview(riasecData) {
  const container = document.getElementById('riasec-preview');
  if (!container) return;
  
  const topTypes = Object.entries(riasecData)
    .map(function(entry) {
      var key = entry[0];
      var value = entry[1];
      return { key: key, value: value, name: getRiasecDisplayName(key) };
    })
    .sort(function(a, b) { return b.value - a.value; })
    .slice(0, 3);
  
  var html = '';
  for (var i = 0; i < topTypes.length; i++) {
    var type = topTypes[i];
    html += '<div class="flex justify-between items-center py-2">' +
      '<span class="text-sm text-gray-700 font-medium">' + type.name + '</span>' +
      '<span class="text-sm font-semibold text-blue-600">' + type.value + '%</span>' +
      '</div>';
  }
  container.innerHTML = html;
}

function displayOceanPreview(oceanData) {
  const container = document.getElementById('ocean-preview');
  if (!container) return;
  
  const topDimensions = Object.entries(oceanData)
    .map(function(entry) {
      var key = entry[0];
      var value = entry[1];
      return { key: key, value: value, name: getOceanDisplayName(key) };
    })
    .sort(function(a, b) { return b.value - a.value; })
    .slice(0, 3);
  
  var html = '';
  for (var i = 0; i < topDimensions.length; i++) {
    var dimension = topDimensions[i];
    html += '<div class="flex justify-between items-center py-2">' +
      '<span class="text-sm text-gray-700 font-medium">' + dimension.name + '</span>' +
      '<span class="text-sm font-semibold text-green-600">' + dimension.value + '%</span>' +
      '</div>';
  }
  container.innerHTML = html;
}

// Helper functions (unchanged)
function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Creativity',
    curiosity: 'Curiosity',
    judgment: 'Judgment',
    loveOfLearning: 'Love of Learning',
    perspective: 'Perspective',
    bravery: 'Bravery',
    perseverance: 'Perseverance',
    honesty: 'Honesty',
    zest: 'Zest',
    love: 'Love',
    kindness: 'Kindness',
    socialIntelligence: 'Social Intelligence',
    teamwork: 'Teamwork',
    fairness: 'Fairness',
    leadership: 'Leadership',
    forgiveness: 'Forgiveness',
    humility: 'Humility',
    prudence: 'Prudence',
    selfRegulation: 'Self-Regulation',
    appreciationOfBeauty: 'Appreciation of Beauty',
    gratitude: 'Gratitude',
    hope: 'Hope',
    humor: 'Humor',
    spirituality: 'Spirituality'
  };
  return displayNames[key] || key;
}

function getRiasecDisplayName(key) {
  const displayNames = {
    realistic: 'Realistic',
    investigative: 'Investigative',
    artistic: 'Artistic',
    social: 'Social',
    enterprising: 'Enterprising',
    conventional: 'Conventional'
  };
  return displayNames[key] || key;
}

function getOceanDisplayName(key) {
  const displayNames = {
    openness: 'Openness',
    conscientiousness: 'Conscientiousness',
    extraversion: 'Extraversion',
    agreeableness: 'Agreeableness',
    neuroticism: 'Neuroticism'
  };
  return displayNames[key] || key;
}

function getPersonaProfileExample() {
  return {
    archetype: "The Analytical Innovator",
    shortSummary: "You are an analytical thinker with strong investigative tendencies and high creativity. The combination of logical-mathematical intelligence and openness to new experiences makes you excel at solving complex problems with innovative approaches.",
    riskTolerance: "Moderate"
  };
}

function getSampleAssessmentResults() {
  return {
    ocean: {
      openness: 85,
      conscientiousness: 78,
      extraversion: 45,
      agreeableness: 62,
      neuroticism: 35
    },
    riasec: {
      realistic: 25,
      investigative: 92,
      artistic: 78,
      social: 45,
      enterprising: 55,
      conventional: 68
    },
    viaIs: {
      creativity: 88,
      curiosity: 92,
      judgment: 85,
      loveOfLearning: 90,
      perspective: 82,
      bravery: 65,
      perseverance: 78,
      honesty: 75,
      zest: 58,
      love: 62,
      kindness: 68,
      socialIntelligence: 55,
      teamwork: 48,
      fairness: 72,
      leadership: 65,
      forgiveness: 58,
      humility: 62,
      prudence: 85,
      selfRegulation: 75,
      appreciationOfBeauty: 82,
      gratitude: 68,
      hope: 72,
      humor: 55,
      spirituality: 45
    }
  };
}

// Action functions (unchanged)
export function downloadResult() {
  alert('PDF download feature coming soon');
}

export function shareResult() {
  if (navigator.share) {
    navigator.share({
      title: 'My Assessment Results',
      text: 'Check out my talent assessment results',
      url: window.location.href
    });
  } else {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
      alert('Assessment results link copied to clipboard');
    });
  }
}

export function retakeAssessment() {
  if (confirm('Are you sure you want to retake the assessment? Previous results will be overwritten.')) {
    // Clear all assessment data including persona profile
    localStorage.removeItem('assessmentAnswers');
    localStorage.removeItem('assessmentCompleted');
    localStorage.removeItem('assessmentResultReady');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('lastSaveResult');
    localStorage.removeItem('lastAssessmentId');
    localStorage.removeItem('personaProfile');
    // Navigate to assessment
    navigateTo('assessment');
  }
}

export function scheduleConsultation() {
  const personaProfile = JSON.parse(localStorage.getItem('personaProfile') || '{}');
  const archetype = personaProfile.archetype || 'Unknown';
  alert('Consultation scheduling feature coming soon.\n' +
        'Based on your "' + archetype + '" profile, we will recommend the right counselor for you.');
}