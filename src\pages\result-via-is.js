// Import utilities for VIA-IS assessment details and charts
import { VIA_IS_CATEGORIES, VIA_IS_STRENGTH_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultViaIsPage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50 to-indigo-50">
      <!-- Futuristic Navigation -->
      <nav class="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/80 border-b border-gray-200/60">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-8">
              <button onclick="navigateTo('result')"
                class="group flex items-center text-gray-500 hover:text-gray-900 transition-all duration-300">
                <div class="w-8 h-8 mr-3 rounded-full bg-gray-100 group-hover:bg-gray-200 flex items-center justify-center transition-all duration-300">
                  <svg class="w-4 h-4 group-hover:-translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                </div>
                <span class="font-medium tracking-wide">Back to Summary</span>
              </button>
              <div class="h-4 w-px bg-gray-300"></div>
              <h1 class="text-lg font-semibold text-gray-900 tracking-wide">VIA-IS Character Strengths</h1>
            </div>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-3 px-4 py-2 rounded-full bg-purple-50 border border-purple-100">
                <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-purple-700">Complete</span>
              </div>
              <button onclick="navigateTo('result-riasec')"
                class="px-6 py-2.5 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-full font-medium hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                Next: RIASEC
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Progress Indicator -->
      <div class="pt-20 pb-8">
        <div class="max-w-7xl mx-auto px-6">
          <div class="relative">
            <div class="h-1 bg-gradient-to-r from-gray-200 via-gray-200 to-gray-200 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-purple-400 via-indigo-500 to-violet-600 rounded-full transition-all duration-1000 ease-out" style="width: 40%"></div>
            </div>
            <div class="absolute -top-2 right-3/5 transform translate-x-2">
              <div class="w-5 h-5 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto px-6 pb-12">
        <!-- Hero Section -->
        <div class="relative mb-12">
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl p-10 border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500">
            <div class="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-indigo-500/5 to-violet-500/5 rounded-3xl"></div>
            <div class="relative z-10">
              <div class="flex items-start space-x-8">
                <div class="flex-shrink-0">
                  <div class="w-28 h-28 bg-gradient-to-br from-purple-500 via-indigo-600 to-violet-700 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <span class="text-5xl relative z-10">💎</span>
                  </div>
                </div>
                <div class="flex-1">
                  <h1 class="text-4xl font-bold text-gray-900 mb-4 tracking-tight">VIA-IS Character Strengths</h1>
                  <p class="text-gray-600 text-lg leading-relaxed mb-8 max-w-3xl">
                    Values in Action Inventory identifies your 24 character strengths across 6 universal virtue categories,
                    providing a comprehensive foundation for personal excellence and optimal development.
                  </p>
                  <div class="flex flex-wrap gap-4">
                    <div class="px-5 py-2.5 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-full border border-purple-100">
                      <span class="text-sm font-medium text-purple-700">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Scientifically Validated
                      </span>
                    </div>
                    <div class="px-5 py-2.5 bg-gradient-to-r from-indigo-50 to-violet-50 rounded-full border border-indigo-100">
                      <span class="text-sm font-medium text-indigo-700">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        Globally Recognized
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Executive Summary -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-gray-900">Executive Summary</h2>
              <p class="text-gray-600 mt-1">Overview of your character strengths profile</p>
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="executive-summary">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Key Insights & Visualization -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Top Strengths Card -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-900">Top 5 Character Strengths</h3>
                <p class="text-gray-600 text-sm mt-1">Your signature character strengths</p>
              </div>
            </div>
            <div class="space-y-4" id="top-strengths-compact">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>

          <!-- Category Overview -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-900">Category Profile</h3>
                <p class="text-gray-600 text-sm mt-1">Strength distribution by virtue categories</p>
              </div>
            </div>
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <canvas id="viaIsCategoriesRadarChart" class="w-full h-64"></canvas>
            </div>
          </div>
        </div>

        <!-- Detailed Analysis -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-semibold text-gray-900">In-Depth Analysis</h2>
              <p class="text-gray-600 mt-1">Detailed exploration of each category and character strength</p>
            </div>
          </div>

          <!-- Category Tabs -->
          <div class="border-b border-gray-200/60 mb-6">
            <nav class="flex space-x-8" id="category-tabs">
              <!-- Will be populated by JavaScript -->
            </nav>
          </div>

          <!-- Tab Content -->
          <div id="category-content">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Complete Strengths Overview -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">All 24 Character Strengths</h2>
                <p class="text-gray-600 mt-1">Complete profile with detailed scores</p>
              </div>
            </div>
            <button class="px-4 py-2 bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700 rounded-xl hover:from-purple-200 hover:to-indigo-200 transition-all duration-300 text-sm font-medium" onclick="toggleFullChart()">
              <span id="chart-toggle-text">Show Chart</span>
            </button>
          </div>
          <div class="grid grid-cols-1 lg:grid-cols-4 gap-4" id="all-strengths-grid">
            <!-- Will be populated by JavaScript -->
          </div>
          <div id="full-chart-container" class="mt-6 hidden">
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <canvas id="viaIsStrengthsBarChart" class="w-full h-96"></canvas>
            </div>
          </div>
        </div>

        <!-- About VIA-IS -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
              </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900">About VIA-IS Character Strengths</h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Background</h3>
              <div class="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  VIA-IS was developed by Dr. Christopher Peterson and Dr. Martin Seligman as
                  part of the Positive Psychology movement to identify universal character strengths.
                </p>
                <p>
                  This assessment measures 24 character strengths that are scientifically proven to contribute
                  to well-being, life satisfaction, and optimal performance.
                </p>
              </div>
            </div>

            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Application Benefits</h3>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Enhance self-awareness and confidence</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Optimize performance in career and teams</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Develop leadership and communication</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Increase resilience and adaptability</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Final Navigation -->
        <div class="flex justify-between items-center">
          <button onclick="navigateTo('result')"
            class="group flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all duration-300 border border-gray-200 hover:border-gray-300">
            <svg class="w-5 h-5 mr-3 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            <span class="font-medium">Back to Summary</span>
          </button>
          <button onclick="navigateTo('result-riasec')"
            class="group flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <span class="font-medium mr-3">Continue to RIASEC</span>
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultViaIs() {
  loadViaIsData();
  initializeTabs();
}

function loadViaIsData() {
  try {
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleViaIsData();
    
    console.log('VIA-IS assessment results loaded:', assessmentResults);
    displayViaIsData(assessmentResults.viaIs || assessmentResults);
  } catch (error) {
    console.error('Error loading VIA-IS data:', error);
    displayViaIsData(getSampleViaIsData());
  }
}

function getSampleViaIsData() {
  return {
    creativity: 88, curiosity: 92, judgment: 85, loveOfLearning: 90, perspective: 82,
    bravery: 65, perseverance: 78, honesty: 75, zest: 58,
    love: 62, kindness: 68, socialIntelligence: 55,
    teamwork: 48, fairness: 72, leadership: 65,
    forgiveness: 58, humility: 62, prudence: 85, selfRegulation: 75,
    appreciationOfBeauty: 82, gratitude: 68, hope: 72, humor: 55, spirituality: 45
  };
}

function displayViaIsData(viaIsData) {
  displayExecutiveSummary(viaIsData);
  displayTopStrengths(viaIsData);
  createViaIsCategoriesRadarChart(viaIsData);
  setupCategoryTabs(viaIsData);
  displayAllStrengthsGrid(viaIsData);
  createViaIsStrengthsBarChart(viaIsData);
}

function displayExecutiveSummary(viaIsData) {
  const container = document.getElementById('executive-summary');
  if (!container) return;

  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);
  const overallAverage = Math.round(Object.values(categoryAverages).reduce((a, b) => a + b, 0) / Object.keys(categoryAverages).length);
  const topCategory = Object.entries(categoryAverages).reduce((a, b) => a[1] > b[1] ? a : b);
  const strengthsAbove80 = Object.values(viaIsData).filter(score => score >= 80).length;

  container.innerHTML = `
    <div class="text-center p-6 bg-indigo-50 rounded-xl border border-indigo-100">
      <div class="text-3xl font-bold text-indigo-600 mb-2">${overallAverage}%</div>
      <div class="text-sm font-medium text-indigo-800">Overall Score</div>
      <div class="text-xs text-indigo-600 mt-1">Character Strengths</div>
    </div>
    
    <div class="text-center p-6 bg-green-50 rounded-xl border border-green-100">
      <div class="text-3xl font-bold text-green-600 mb-2">${strengthsAbove80}</div>
      <div class="text-sm font-medium text-green-800">Signature Strengths</div>
      <div class="text-xs text-green-600 mt-1">Score ≥ 80%</div>
    </div>
    
    <div class="text-center p-6 bg-purple-50 rounded-xl border border-purple-100">
      <div class="text-lg font-bold text-purple-600 mb-2">${VIA_IS_CATEGORIES[topCategory[0]].name}</div>
      <div class="text-sm font-medium text-purple-800">Dominant Category</div>
      <div class="text-xs text-purple-600 mt-1">${Math.round(topCategory[1])}% Average</div>
    </div>
  `;
}

function displayTopStrengths(viaIsData) {
  const container = document.getElementById('top-strengths-compact');
  if (!container) return;

  const strengthsArray = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key),
    details: VIA_IS_STRENGTH_DETAILS[key]
  }));

  const topStrengths = strengthsArray.sort((a, b) => b.score - a.score).slice(0, 5);

  container.innerHTML = topStrengths.map((strength, index) => {
    const rankColors = ['text-yellow-600', 'text-orange-600', 'text-red-600', 'text-purple-600', 'text-indigo-600'];
    const bgColors = ['bg-yellow-50', 'bg-orange-50', 'bg-red-50', 'bg-purple-50', 'bg-indigo-50'];

    return `
      <div class="flex items-center justify-between p-4 ${bgColors[index]} rounded-lg border">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 ${rankColors[index]} bg-white rounded-full flex items-center justify-center font-bold text-sm">
            ${index + 1}
          </div>
          <div>
            <div class="font-semibold text-gray-900">${strength.displayName}</div>
            <div class="text-xs text-gray-600">${getCategoryForStrength(strength.name)}</div>
          </div>
        </div>
        <div class="text-xl font-bold ${rankColors[index]}">${strength.score}%</div>
      </div>
    `;
  }).join('');
}

function setupCategoryTabs(viaIsData) {
  const tabsContainer = document.getElementById('category-tabs');
  const contentContainer = document.getElementById('category-content');
  if (!tabsContainer || !contentContainer) return;

  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);
  const sortedCategories = Object.entries(VIA_IS_CATEGORIES)
    .sort((a, b) => categoryAverages[b[0]] - categoryAverages[a[0]]);

  // Create tabs
  tabsContainer.innerHTML = sortedCategories.map(([key, category], index) => `
    <button class="py-4 px-1 border-b-2 font-medium text-sm transition-colors category-tab ${index === 0 ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}"
      data-category="${key}">
      <div class="flex items-center space-x-2">
        <span>${category.icon}</span>
        <span>${category.name}</span>
        <span class="text-xs bg-gray-100 px-2 py-1 rounded">${Math.round(categoryAverages[key])}%</span>
      </div>
    </button>
  `).join('');

  // Show first category by default
  if (sortedCategories.length > 0) {
    showCategoryContent(sortedCategories[0][0], viaIsData);
  }

  // Add click handlers
  document.querySelectorAll('.category-tab').forEach(tab => {
    tab.addEventListener('click', (e) => {
      // Remove active state from all tabs
      document.querySelectorAll('.category-tab').forEach(t => {
        t.classList.remove('border-indigo-500', 'text-indigo-600');
        t.classList.add('border-transparent', 'text-gray-500');
      });
      
      // Add active state to clicked tab
      tab.classList.remove('border-transparent', 'text-gray-500');
      tab.classList.add('border-indigo-500', 'text-indigo-600');
      
      showCategoryContent(tab.dataset.category, viaIsData);
    });
  });
}

function showCategoryContent(categoryKey, viaIsData) {
  const container = document.getElementById('category-content');
  if (!container) return;

  const category = VIA_IS_CATEGORIES[categoryKey];
  const categoryAverages = ChartUtils.calculateViaIsCategoryAverages(viaIsData);
  const strengths = category.strengths.map(strength => ({
    name: strength,
    score: viaIsData[strength] || 0,
    displayName: getViaIsDisplayName(strength),
    details: VIA_IS_STRENGTH_DETAILS[strength]
  })).sort((a, b) => b.score - a.score);

  container.innerHTML = `
    <div class="mb-6">
      <div class="flex items-start space-x-4">
        <div class="text-4xl">${category.icon}</div>
        <div class="flex-1">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">${category.name}</h3>
          <p class="text-gray-600 mb-4">${category.description}</p>
          <div class="flex items-center space-x-4">
            <div class="text-2xl font-bold text-indigo-600">${Math.round(categoryAverages[categoryKey])}%</div>
            <div class="flex-1">
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-indigo-600 h-2 rounded-full transition-all duration-500" 
                  style="width: ${categoryAverages[categoryKey]}%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      ${strengths.map(strength => {
        const level = strength.score >= 80 ? 'high' : strength.score >= 60 ? 'medium' : 'low';
        const levelClasses = {
          high: 'bg-green-50 border-green-200 text-green-800',
          medium: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          low: 'bg-blue-50 border-blue-200 text-blue-800'
        };
        const scoreClasses = {
          high: 'text-green-600',
          medium: 'text-yellow-600',
          low: 'text-blue-600'
        };

        return `
          <div class="border rounded-lg p-4 ${levelClasses[level]}">
            <div class="flex justify-between items-center mb-3">
              <h4 class="font-semibold">${strength.displayName}</h4>
              <span class="text-xl font-bold ${scoreClasses[level]}">${strength.score}%</span>
            </div>
            <p class="text-sm opacity-90 leading-relaxed mb-3">${strength.details?.description || ''}</p>
            <div class="w-full bg-white bg-opacity-50 rounded-full h-2">
              <div class="${scoreClasses[level].replace('text-', 'bg-')} h-2 rounded-full transition-all duration-500" 
                style="width: ${strength.score}%"></div>
            </div>
          </div>
        `;
      }).join('')}
    </div>
  `;
}

function displayAllStrengthsGrid(viaIsData) {
  const container = document.getElementById('all-strengths-grid');
  if (!container) return;

  const allStrengths = Object.entries(viaIsData).map(([key, value]) => ({
    name: key,
    score: value,
    displayName: getViaIsDisplayName(key),
    category: getCategoryForStrength(key)
  })).sort((a, b) => b.score - a.score);

  container.innerHTML = allStrengths.map(strength => {
    const level = strength.score >= 80 ? 'high' : strength.score >= 60 ? 'medium' : 'low';
    const levelClasses = {
      high: 'bg-green-50 border-green-200',
      medium: 'bg-yellow-50 border-yellow-200', 
      low: 'bg-blue-50 border-blue-200'
    };
    const scoreClasses = {
      high: 'text-green-600',
      medium: 'text-yellow-600',
      low: 'text-blue-600'
    };

    return `
      <div class="border rounded-lg p-4 ${levelClasses[level]} hover:shadow-md transition-shadow">
        <div class="flex justify-between items-center mb-2">
          <div class="font-medium text-gray-900 text-sm">${strength.displayName}</div>
          <div class="text-lg font-bold ${scoreClasses[level]}">${strength.score}%</div>
        </div>
        <div class="text-xs text-gray-600 mb-2">${strength.category}</div>
        <div class="w-full bg-white bg-opacity-50 rounded-full h-1.5">
          <div class="${scoreClasses[level].replace('text-', 'bg-')} h-1.5 rounded-full transition-all duration-300" 
            style="width: ${strength.score}%"></div>
        </div>
      </div>
    `;
  }).join('');
}

function createViaIsCategoriesRadarChart(viaIsData) {
  const ctx = document.getElementById('viaIsCategoriesRadarChart');
  if (!ctx) return;
  return ChartUtils.createViaIsCategoriesRadarChart(ctx, viaIsData);
}

function createViaIsStrengthsBarChart(viaIsData) {
  const ctx = document.getElementById('viaIsStrengthsBarChart');
  if (!ctx) return;
  return ChartUtils.createViaIsStrengthsBarChart(ctx, viaIsData);
}

function initializeTabs() {
  // Tab functionality is handled in setupCategoryTabs
}

// Toggle full chart visibility
window.toggleFullChart = function() {
  const container = document.getElementById('full-chart-container');
  const toggleText = document.getElementById('chart-toggle-text');
  
  if (container.classList.contains('hidden')) {
    container.classList.remove('hidden');
    toggleText.textContent = 'Sembunyikan Chart';
  } else {
    container.classList.add('hidden');
    toggleText.textContent = 'Tampilkan Chart';
  }
};

function getViaIsDisplayName(key) {
  const displayNames = {
    creativity: 'Creativity',
    curiosity: 'Curiosity',
    judgment: 'Judgment',
    loveOfLearning: 'Love of Learning',
    perspective: 'Perspective',
    bravery: 'Bravery',
    perseverance: 'Perseverance',
    honesty: 'Honesty',
    zest: 'Zest',
    love: 'Love',
    kindness: 'Kindness',
    socialIntelligence: 'Social Intelligence',
    teamwork: 'Teamwork',
    fairness: 'Fairness',
    leadership: 'Leadership',
    forgiveness: 'Forgiveness',
    humility: 'Humility',
    prudence: 'Prudence',
    selfRegulation: 'Self-Regulation',
    appreciationOfBeauty: 'Appreciation of Beauty',
    gratitude: 'Gratitude',
    hope: 'Hope',
    humor: 'Humor',
    spirituality: 'Spirituality'
  };
  return displayNames[key] || key;
}

function getCategoryForStrength(strengthKey) {
  for (const [categoryKey, category] of Object.entries(VIA_IS_CATEGORIES)) {
    if (category.strengths.includes(strengthKey)) {
      return VIA_IS_CATEGORIES[categoryKey].name;
    }
  }
  return 'Unknown';
}

