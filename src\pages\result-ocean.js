// Import utilities for OCEAN assessment details and charts
import { OCEAN_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultOceanPage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50">
      <!-- Futuristic Navigation -->
      <nav class="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/80 border-b border-gray-200/60">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-8">
              <button onclick="navigateTo('result-riasec')"
                class="group flex items-center text-gray-500 hover:text-gray-900 transition-all duration-300">
                <div class="w-8 h-8 mr-3 rounded-full bg-gray-100 group-hover:bg-gray-200 flex items-center justify-center transition-all duration-300">
                  <svg class="w-4 h-4 group-hover:-translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                </div>
                <span class="font-medium tracking-wide">RIASEC</span>
              </button>
              <div class="h-4 w-px bg-gray-300"></div>
              <h1 class="text-lg font-semibold text-gray-900 tracking-wide">OCEAN Personality</h1>
            </div>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-3 px-4 py-2 rounded-full bg-emerald-50 border border-emerald-100">
                <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-emerald-700">Complete</span>
              </div>
              <button onclick="navigateTo('result-persona')"
                class="px-6 py-2.5 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-full font-medium hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                Next: Persona Profile
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Progress Indicator -->
      <div class="pt-20 pb-8">
        <div class="max-w-7xl mx-auto px-6">
          <div class="relative">
            <div class="h-1 bg-gradient-to-r from-gray-200 via-gray-200 to-gray-200 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-emerald-400 via-teal-500 to-green-600 rounded-full transition-all duration-1000 ease-out" style="width: 80%"></div>
            </div>
            <div class="absolute -top-2 right-1/5 transform translate-x-2">
              <div class="w-5 h-5 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto px-6 pb-12">
        <!-- Hero Section -->
        <div class="relative mb-12">
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl p-10 border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500">
            <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-teal-500/5 to-green-500/5 rounded-3xl"></div>
            <div class="relative z-10">
              <div class="flex items-start space-x-8">
                <div class="flex-shrink-0">
                  <div class="w-28 h-28 bg-gradient-to-br from-emerald-500 via-teal-600 to-green-700 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <span class="text-5xl relative z-10">🌊</span>
                  </div>
                </div>
                <div class="flex-1">
                  <h1 class="text-4xl font-bold text-gray-900 mb-4 tracking-tight">OCEAN Big Five Personality</h1>
                  <p class="text-gray-600 text-lg leading-relaxed mb-8 max-w-3xl">
                    The Big Five model measures 5 universal personality dimensions, providing a comprehensive view
                    of your behavioral patterns, thinking styles, and emotional responses across various situations.
                  </p>
                  <div class="flex flex-wrap gap-4">
                    <div class="px-5 py-2.5 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-full border border-emerald-100">
                      <span class="text-sm font-medium text-emerald-700">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Scientifically Robust
                      </span>
                    </div>
                    <div class="px-5 py-2.5 bg-gradient-to-r from-teal-50 to-green-50 rounded-full border border-teal-100">
                      <span class="text-sm font-medium text-teal-700">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        Universally Accepted
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- OCEAN Explanation -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">About OCEAN Big Five Personality</h3>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">What is Big Five?</h4>
              <div class="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  The Big Five or OCEAN model is the most scientifically accepted personality model
                  widely used in personality psychology.
                </p>
                <p>
                  This model identifies 5 core personality dimensions that are relatively stable throughout life
                  and can predict various aspects of behavior, performance, and well-being.
                </p>
                <p>
                  OCEAN stands for: <strong>O</strong>penness, <strong>C</strong>onscientiousness,
                  <strong>E</strong>xtraversion, <strong>A</strong>greeableness, and <strong>N</strong>euroticism.
                </p>
              </div>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Benefits of Understanding Big Five</h4>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Deep and accurate self-understanding</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Predicting work and academic performance</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Improving interpersonal relationships</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Guidance for personal development</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Understanding communication and work styles</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- OCEAN Dimensions Overview -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-emerald-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">5 OCEAN Personality Dimensions</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="ocean-dimensions-overview">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- User's OCEAN Profile -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Radar Chart -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900">Radar Chart Visualization</h4>
            </div>
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <canvas id="oceanRadarChart" width="400" height="400"></canvas>
            </div>
          </div>

          <!-- Personality Summary -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900">Personality Summary</h4>
            </div>
            <div id="personality-summary" class="space-y-4">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Detailed OCEAN Results -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">Detailed OCEAN Assessment Results</h3>
          </div>
          <div id="ocean-detailed-results" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Final Navigation -->
        <div class="flex justify-between items-center">
          <button onclick="navigateTo('result-riasec')"
            class="group flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all duration-300 border border-gray-200 hover:border-gray-300">
            <svg class="w-5 h-5 mr-3 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            <span class="font-medium">Back to RIASEC</span>
          </button>
          <button onclick="navigateTo('result-persona')"
            class="group flex items-center px-8 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <span class="font-medium mr-3">Continue to Persona Profile</span>
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultOcean() {
  // Load OCEAN assessment data
  loadOceanData();
}

function loadOceanData() {
  try {
    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleOceanData();

    console.log('OCEAN assessment results loaded:', assessmentResults);

    displayOceanData(assessmentResults.ocean || assessmentResults);
  } catch (error) {
    console.error('Error loading OCEAN data:', error);
    // Fallback to sample data
    displayOceanData(getSampleOceanData());
  }
}

function getSampleOceanData() {
  return {
    openness: 85,
    conscientiousness: 78,
    extraversion: 45,
    agreeableness: 62,
    neuroticism: 35
  };
}

function displayOceanData(oceanData) {
  // Display dimensions overview
  displayDimensionsOverview();

  // Create radar chart
  createOceanRadarChart(oceanData);

  // Display personality summary
  displayPersonalitySummary(oceanData);

  // Display detailed results
  displayDetailedResults(oceanData);
}

function displayDimensionsOverview() {
  const container = document.getElementById('ocean-dimensions-overview');
  if (!container) return;

  const oceanDimensions = [
    {
      code: 'O',
      name: 'Openness',
      fullName: 'Openness to Experience',
      description: 'Keterbukaan terhadap pengalaman baru, kreativitas, dan ide-ide abstrak',
      color: 'bg-purple-50 border-purple-200 text-purple-800',
      icon: '🎨'
    },
    {
      code: 'C',
      name: 'Conscientiousness',
      fullName: 'Conscientiousness',
      description: 'Kedisiplinan, keteraturan, dan orientasi pada tujuan',
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: '📋'
    },
    {
      code: 'E',
      name: 'Extraversion',
      fullName: 'Extraversion',
      description: 'Energi yang diarahkan keluar, sosiabilitas, dan assertiveness',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: '🎉'
    },
    {
      code: 'A',
      name: 'Agreeableness',
      fullName: 'Agreeableness',
      description: 'Kecenderungan untuk kooperatif, empati, dan kepercayaan',
      color: 'bg-green-50 border-green-200 text-green-800',
      icon: '🤝'
    },
    {
      code: 'N',
      name: 'Neuroticism',
      fullName: 'Neuroticism',
      description: 'Kecenderungan mengalami emosi negatif dan ketidakstabilan emosional',
      color: 'bg-red-50 border-red-200 text-red-800',
      icon: '😰'
    }
  ];

  container.innerHTML = oceanDimensions.map(dimension => `
    <div class="border rounded-lg p-6 ${dimension.color}">
      <div class="flex items-center mb-4">
        <span class="text-3xl mr-4">${dimension.icon}</span>
        <div>
          <h4 class="font-semibold text-lg">${dimension.code} - ${dimension.name}</h4>
          <p class="text-xs opacity-75">${dimension.fullName}</p>
        </div>
      </div>
      <p class="text-sm">${dimension.description}</p>
    </div>
  `).join('');
}

function createOceanRadarChart(oceanData) {
  const ctx = document.getElementById('oceanRadarChart');
  if (!ctx) return;

  return ChartUtils.createOceanRadarChart(ctx, oceanData);
}

function displayPersonalitySummary(oceanData) {
  const container = document.getElementById('personality-summary');
  if (!container) return;

  // Sort dimensions by score
  const sortedDimensions = Object.entries(oceanData)
    .map(([key, value]) => ({
      key,
      value,
      details: OCEAN_DETAILS[key],
      name: OCEAN_DETAILS[key]?.name || key
    }))
    .sort((a, b) => b.value - a.value);

  const highest = sortedDimensions[0];
  const lowest = sortedDimensions[sortedDimensions.length - 1];

  container.innerHTML = `
    <div class="space-y-4">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <h5 class="font-semibold text-green-800 mb-2">Dimensi Tertinggi</h5>
        <div class="flex justify-between items-center">
          <span class="text-green-700">${highest.name}</span>
          <span class="text-green-600 font-bold text-lg">${highest.value}%</span>
        </div>
        <p class="text-green-600 text-sm mt-2">${highest.details?.description || ''}</p>
      </div>

      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h5 class="font-semibold text-orange-800 mb-2">Dimensi Terendah</h5>
        <div class="flex justify-between items-center">
          <span class="text-orange-700">${lowest.name}</span>
          <span class="text-orange-600 font-bold text-lg">${lowest.value}%</span>
        </div>
        <p class="text-orange-600 text-sm mt-2">${lowest.details?.description || ''}</p>
      </div>

      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 class="font-semibold text-blue-800 mb-2">Profil Kepribadian</h5>
        <div class="space-y-2">
          ${sortedDimensions.map(dim => `
            <div class="flex justify-between items-center">
              <span class="text-blue-700 text-sm">${dim.name}</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-blue-200 rounded-full h-2">
                  <div class="bg-blue-500 h-2 rounded-full" style="width: ${dim.value}%"></div>
                </div>
                <span class="text-blue-600 font-medium text-sm">${dim.value}%</span>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
}

function displayDetailedResults(oceanData) {
  const container = document.getElementById('ocean-detailed-results');
  if (!container) return;

  // Sort OCEAN scores to show highest first
  const sortedOcean = Object.entries(oceanData)
    .map(([key, value]) => ({ key, value, details: OCEAN_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedOcean.map(({ value, details }) => {
    const isHigh = value >= 60;
    const scoreData = isHigh ? details.highScore : details.lowScore;

    return `
      <div class="border rounded-lg p-4 ${value >= 70 ? 'border-green-300 bg-green-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
        <div class="flex justify-between items-center mb-3">
          <h4 class="font-semibold text-lg text-gray-900">${details.name}</h4>
          <div class="text-right">
            <div class="text-xl font-bold ${value >= 70 ? 'text-green-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
            <div class="text-xs ${value >= 70 ? 'text-green-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
              ${value >= 60 ? 'Tinggi' : value >= 40 ? 'Sedang' : 'Rendah'}
            </div>
          </div>
        </div>

        <p class="text-gray-700 text-sm mb-3">${details.description}</p>

        <div class="space-y-3">
          <div>
            <h5 class="font-medium text-gray-800 mb-2 text-sm">Karakteristik (${isHigh ? 'Tinggi' : 'Rendah'}):</h5>
            <ul class="text-gray-600 space-y-1 text-xs">
              ${scoreData.characteristics.slice(0, 3).map(char => `<li class="flex items-start"><span class="text-green-500 mr-1">•</span>${char}</li>`).join('')}
            </ul>
          </div>
          <div>
            <h5 class="font-medium text-gray-800 mb-2 text-sm">Implikasi:</h5>
            <ul class="text-gray-600 space-y-1 text-xs">
              ${scoreData.implications.slice(0, 3).map(impl => `<li class="flex items-start"><span class="text-blue-500 mr-1">•</span>${impl}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;
  }).join('');
}


