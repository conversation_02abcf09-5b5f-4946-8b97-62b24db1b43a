# PetaTalenta Frontend

Platform pemetaan talenta berbasis AI dengan 6 halaman modular yang dapat dikustomisasi sesuai kebutuhan.

## Struktur Halaman

### 1. **Auth** (`/src/pages/auth.js`)
- <PERSON><PERSON> login dan registrasi
- Form validasi email dan password
- Simu<PERSON>i autentikasi
- Responsive design dengan Tailwind CSS

### 2. **Dashboard** (`/src/pages/dashboard.js`)
- <PERSON>aman utama setelah login
- Statistik assessment (cards)
- Navigasi ke halaman lain
- Quick actions untuk memulai assessment

### 3. **Assessment** (`/src/pages/assessment.js`)
- Halaman kuis/assessment talenta
- Progress bar dan navigasi pertanyaan
- Sistem penyimpanan jawaban
- Multiple choice questions

### 4. **Waiting** (`/src/pages/waiting.js`)
- Halaman loading saat memproses assessment
- Animasi loading dan progress steps
- Tips informatif saat menunggu
- Auto-refresh dan estimasi waktu

### 5. **Result** (`/src/pages/result.js`)
- Halaman hasil assessment
- Visualisasi profil talenta
- Breakdown kompetensi dengan progress bars
- Rekomendasi pengembangan dan posisi

### 6. **Profile** (`/src/pages/profile.js`)
- Halaman profil pengguna
- Form data pribadi dan profesional
- Pengaturan akun dan preferensi
- Fitur hapus akun (danger zone)

## Teknologi

- **Vite** - Build tool dan dev server
- **Vanilla JavaScript** - Tanpa framework, modular dan ringan
- **Tailwind CSS** - Utility-first CSS framework
- **ES6 Modules** - Sistem modular modern
- **LocalStorage** - Penyimpanan data lokal

## Instalasi dan Menjalankan

```bash
# Install dependencies
npm install

# Jalankan development server
npm run dev

# Build untuk production
npm run build

# Preview build
npm run preview
```

## Struktur File

```
src/
├── pages/           # Halaman-halaman modular
│   ├── auth.js      # Halaman autentikasi
│   ├── dashboard.js # Dashboard utama
│   ├── assessment.js# Assessment/kuis
│   ├── waiting.js   # Loading/waiting
│   ├── result.js    # Hasil assessment
│   └── profile.js   # Profil pengguna
├── router.js        # Router dan navigasi
├── main.js          # Entry point aplikasi
└── style.css        # Tailwind CSS dan custom styles
```

## Fitur

### Routing
- Hash-based routing (`#auth`, `#dashboard`, dll)
- Authentication guard
- Automatic navigation
- Page initialization dan cleanup

### State Management
- LocalStorage untuk persistensi data
- Session management
- Assessment data storage

### Responsive Design
- Mobile-first approach
- Tailwind CSS utilities
- Adaptive layouts

### Modular Architecture
- Setiap halaman adalah modul terpisah
- Easy to customize dan extend
- Clean separation of concerns

## Kustomisasi

### Menambah Halaman Baru
1. Buat file baru di `src/pages/`
2. Export function `createPageName()`
3. Tambahkan ke `router.js`
4. Definisikan route dan konfigurasi

### Mengubah Styling
- Edit `src/style.css` untuk custom styles
- Gunakan Tailwind utilities di HTML
- Tambah komponen CSS di `@layer components`

### Menambah Fitur Assessment
- Edit `assessmentQuestions` di `assessment.js`
- Tambah logic scoring di `result.js`
- Customize visualisasi hasil

## Demo Credentials

Untuk testing, gunakan email dan password apa saja. Sistem akan otomatis login.

## Development Notes

- Semua halaman sangat polosan dan mudah dimodifikasi
- Menggunakan localStorage untuk simulasi backend
- Assessment questions dapat dengan mudah diganti
- Styling menggunakan Tailwind CSS yang sangat customizable
- Router sederhana tapi powerful untuk SPA

## Browser Support

- Modern browsers dengan ES6 support
- Chrome, Firefox, Safari, Edge
- Mobile browsers

## License

MIT License - Bebas digunakan dan dimodifikasi sesuai kebutuhan.
