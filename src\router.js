// Import all page modules
import { createAuthPage, handleLogin, handleRegister, showRegister, toggleAuthMode } from './pages/auth.js';
import { authService } from './services/authService.js';
import { createDashboardPage, initDashboard, logout, testAssessmentResults, showAssessmentFormat, testCompleteFlow } from './pages/dashboard.js';
import { createAssessment3PhasePage, initAssessment3Phase, nextQuestion as nextQuestion3Phase, previousQuestion as previousQuestion3Phase, skipQuestion, jumpToPhase, jumpToCategory, handleAnswerChange, saveAndExit } from './pages/assessment-3phase.js';
import { createWaitingPage, initWaiting, checkResults, cleanupWaiting } from './pages/waiting.js';
import { createBriefResultPage, initBriefResult, downloadResult, shareResult, retakeAssessment, scheduleConsultation } from './pages/brief-result.js';
import { createProfilePage, initProfile, saveProfile, changeAvatar, deleteAccount } from './pages/profile.js';
import { createResultViaIsPage, initResultViaIs } from './pages/result-via-is.js';
import { createResultRiasecPage, initResultRiasec } from './pages/result-riasec.js';
import { createResultOceanPage, initResultOcean } from './pages/result-ocean.js';
import { createResultPersonaPage, initResultPersona } from './pages/result-persona.js';

// Page configurations
const pages = {
  auth: {
    component: createAuthPage,
    init: null,
    cleanup: null,
    requiresAuth: false
  },
  dashboard: {
    component: createDashboardPage,
    init: initDashboard,
    cleanup: null,
    requiresAuth: true
  },
  assessment: {
    component: createAssessment3PhasePage,
    init: initAssessment3Phase,
    cleanup: null,
    requiresAuth: true
  },
  waiting: {
    component: createWaitingPage,
    init: initWaiting,
    cleanup: cleanupWaiting,
    requiresAuth: true
  },
  result: {
    component: createBriefResultPage,
    init: initBriefResult,
    cleanup: null,
    requiresAuth: true
  },
  profile: {
    component: createProfilePage,
    init: initProfile,
    cleanup: null,
    requiresAuth: true
  },
  'result-via-is': {
    component: createResultViaIsPage,
    init: initResultViaIs,
    cleanup: null,
    requiresAuth: true
  },
  'result-riasec': {
    component: createResultRiasecPage,
    init: initResultRiasec,
    cleanup: null,
    requiresAuth: true
  },
  'result-ocean': {
    component: createResultOceanPage,
    init: initResultOcean,
    cleanup: null,
    requiresAuth: true
  },
  'result-persona': {
    component: createResultPersonaPage,
    init: initResultPersona,
    cleanup: null,
    requiresAuth: true
  }
};

let currentPage = null;

// Router class
class Router {
  constructor() {
    this.currentRoute = null;
    this.init();
  }

  init() {
    // Listen for hash changes
    window.addEventListener('hashchange', () => {
      this.handleRoute();
    });

    // Handle initial route
    this.handleRoute();
  }

  handleRoute() {
    const hash = window.location.hash.slice(1) || 'auth';
    this.navigateTo(hash);
  }

  navigateTo(route) {
    const page = pages[route];

    if (!page) {
      console.error(`Page "${route}" not found`);
      this.navigateTo('auth');
      return;
    }

    // Check authentication
    if (page.requiresAuth && !this.isAuthenticated()) {
      this.navigateTo('auth');
      return;
    }

    // Cleanup previous page
    if (currentPage && currentPage.cleanup) {
      currentPage.cleanup();
    }

    // Update current page
    currentPage = page;
    this.currentRoute = route;

    // Render page
    this.renderPage(page);

    // Initialize page
    if (page.init) {
      page.init();
    }

    // Update URL hash
    if (window.location.hash.slice(1) !== route) {
      window.location.hash = route;
    }

    // Scroll to top of the page after navigation
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }

  renderPage(page) {
    const app = document.getElementById('app');
    if (app) {
      app.innerHTML = page.component();
    }
  }

  isAuthenticated() {
    // Use auth service to check authentication
    return authService.isAuthenticated();
  }

  async login(userData) {
    // This method is now mainly for programmatic login
    // The actual login is handled by authService in the auth page
    if (userData) {
      localStorage.setItem('userData', JSON.stringify(userData));
    }
    this.navigateTo('dashboard');
  }

  async logout() {
    // Use auth service to logout
    await authService.logout();
    this.navigateTo('auth');
  }

  getCurrentRoute() {
    return this.currentRoute;
  }
}

// Create router instance
const router = new Router();

// Global navigation function
window.navigateTo = (route) => {
  router.navigateTo(route);
};

// Global functions for pages
// Login and register are now handled in auth.js with proper API integration
// Keep these for backward compatibility but they delegate to the auth page functions
window.handleLogin = handleLogin;
window.handleRegister = handleRegister;
window.toggleAuthMode = toggleAuthMode;

window.showRegister = showRegister;
window.logout = () => router.logout();

// Assessment functions
window.nextQuestion = nextQuestion3Phase;
window.previousQuestion = previousQuestion3Phase;
window.skipQuestion = skipQuestion;
window.jumpToPhase = jumpToPhase;
window.jumpToCategory = jumpToCategory;

window.handleAnswerChange = handleAnswerChange;
window.saveAndExit = saveAndExit;

// Waiting functions
window.checkResults = checkResults;

// Result functions
window.downloadResult = downloadResult;
window.shareResult = shareResult;
window.retakeAssessment = retakeAssessment;
window.scheduleConsultation = scheduleConsultation;

// Profile functions
window.saveProfile = saveProfile;
window.changeAvatar = changeAvatar;
window.deleteAccount = deleteAccount;

// Test functions
window.testAssessmentResults = testAssessmentResults;
window.showAssessmentFormat = showAssessmentFormat;
window.testCompleteFlow = testCompleteFlow;

export default router;
