// Authentication Service for API communication
// Handles all authentication-related API calls

export class AuthService {
  constructor() {
    this.baseURL = 'https://api.chhrone.web.id';
    this.endpoints = {
      register: '/api/auth/register',
      login: '/api/auth/login',
      logout: '/api/auth/logout',
      deleteAccount: '/api/auth/account',
      changePassword: '/api/auth/change-password',
      tokenBalance: '/api/auth/token-balance'
    };
  }

  // Get authentication headers
  getAuthHeaders() {
    const token = localStorage.getItem('userToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }

  // Get headers without authentication (for public endpoints)
  getPublicHeaders() {
    return {
      'Content-Type': 'application/json'
    };
  }

  // Validate email format
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 255;
  }

  // Validate password format
  validatePassword(password) {
    // Minimum 8 characters, must contain at least one letter and one number
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  }

  // Register new user
  async register(email, password) {
    try {
      // Validate input
      if (!this.validateEmail(email)) {
        throw new Error('Invalid email format or email too long (max 255 characters)');
      }

      if (!this.validatePassword(password)) {
        throw new Error('Password must be at least 8 characters and contain at least one letter and one number');
      }

      const response = await fetch(`${this.baseURL}${this.endpoints.register}`, {
        method: 'POST',
        headers: this.getPublicHeaders(),
        body: JSON.stringify({
          email,
          password
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP error! status: ${response.status}`);
      }

      // Store authentication data
      if (result.success && result.data) {
        localStorage.setItem('userToken', result.data.token);
        localStorage.setItem('userData', JSON.stringify(result.data.user));
        localStorage.setItem('isLoggedIn', 'true');
      }

      return {
        success: true,
        data: result.data,
        message: result.message
      };

    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Login user
  async login(email, password) {
    try {
      // Validate input
      if (!this.validateEmail(email)) {
        throw new Error('Invalid email format');
      }

      if (!password) {
        throw new Error('Password is required');
      }

      const response = await fetch(`${this.baseURL}${this.endpoints.login}`, {
        method: 'POST',
        headers: this.getPublicHeaders(),
        body: JSON.stringify({
          email,
          password
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP error! status: ${response.status}`);
      }

      // Store authentication data
      if (result.success && result.data) {
        localStorage.setItem('userToken', result.data.token);
        localStorage.setItem('userData', JSON.stringify(result.data.user));
        localStorage.setItem('isLoggedIn', 'true');
      }

      return {
        success: true,
        data: result.data,
        message: result.message
      };

    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Logout user
  async logout() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.logout}`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      // Clear local storage regardless of API response
      this.clearAuthData();

      if (!response.ok) {
        console.warn('Logout API call failed, but local data cleared');
      }

      return {
        success: true,
        message: 'Logged out successfully'
      };

    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local data even if API fails
      this.clearAuthData();
      return {
        success: true,
        message: 'Logged out locally (API unavailable)'
      };
    }
  }

  // Delete user account
  async deleteAccount() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.deleteAccount}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP error! status: ${response.status}`);
      }

      // Clear authentication data after successful deletion
      this.clearAuthData();

      return {
        success: true,
        data: result.data,
        message: result.message
      };

    } catch (error) {
      console.error('Delete account error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    try {
      // Validate input
      if (!currentPassword) {
        throw new Error('Current password is required');
      }

      if (!this.validatePassword(newPassword)) {
        throw new Error('New password must be at least 8 characters and contain at least one letter and one number');
      }

      const response = await fetch(`${this.baseURL}${this.endpoints.changePassword}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        message: result.message || 'Password changed successfully'
      };

    } catch (error) {
      console.error('Change password error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get token balance
  async getTokenBalance() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.tokenBalance}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data: result.data
      };

    } catch (error) {
      console.error('Get token balance error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Clear authentication data from localStorage
  clearAuthData() {
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    localStorage.removeItem('isLoggedIn');
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('userToken');
  }

  // Get current user data
  getCurrentUser() {
    const userData = localStorage.getItem('userData');
    return userData ? JSON.parse(userData) : null;
  }

  // Get current token
  getCurrentToken() {
    return localStorage.getItem('userToken');
  }
}

// Create singleton instance
export const authService = new AuthService();
