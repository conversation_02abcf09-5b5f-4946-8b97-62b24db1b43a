// Assessment Service for API communication
// Handles saving and retrieving assessment results

export class AssessmentService {
  constructor() {
    // Use the same base URL as auth service for consistency
    this.baseURL = 'https://api.chhrone.web.id';
    this.endpoints = {
      submitAssessment: '/api/assessment/submit',
      getAssessmentStatus: '/api/assessment/status',
      getQueueStatus: '/api/assessment/queue/status',
      getJobStatus: '/api/archive/jobs',
      getResult: '/api/archive/results'
    };
  }

  // Get authentication headers
  getAuthHeaders() {
    const token = localStorage.getItem('userToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }

  // Generate idempotency key for duplicate prevention
  generateIdempotencyKey() {
    return `assessment_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // Validate assessment data structure
  validateAssessmentData(data) {
    if (!data || typeof data !== 'object') return false;

    // Check RIASEC data
    if (!data.riasec || typeof data.riasec !== 'object') return false;
    const riasecKeys = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
    if (!riasecKeys.every(key => typeof data.riasec[key] === 'number' && data.riasec[key] >= 0 && data.riasec[key] <= 100)) {
      return false;
    }

    // Check OCEAN data
    if (!data.ocean || typeof data.ocean !== 'object') return false;
    const oceanKeys = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
    if (!oceanKeys.every(key => typeof data.ocean[key] === 'number' && data.ocean[key] >= 0 && data.ocean[key] <= 100)) {
      return false;
    }

    // Check VIA-IS data
    if (!data.viaIs || typeof data.viaIs !== 'object') return false;
    const viaIsKeys = [
      'creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective',
      'bravery', 'perseverance', 'honesty', 'zest',
      'love', 'kindness', 'socialIntelligence',
      'teamwork', 'fairness', 'leadership',
      'forgiveness', 'humility', 'prudence', 'selfRegulation',
      'appreciationOfBeauty', 'gratitude', 'hope', 'humor', 'spirituality'
    ];
    if (!viaIsKeys.every(key => typeof data.viaIs[key] === 'number' && data.viaIs[key] >= 0 && data.viaIs[key] <= 100)) {
      return false;
    }

    return true;
  }

  // Submit assessment for AI processing
  async submitAssessment(assessmentData) {
    try {
      // Validate assessment data structure
      if (!this.validateAssessmentData(assessmentData)) {
        throw new Error('Invalid assessment data structure');
      }

      // Prepare payload according to API specification
      const payload = {
        assessmentName: "AI-Driven Talent Mapping",
        riasec: assessmentData.riasec,
        ocean: assessmentData.ocean,
        viaIs: assessmentData.viaIs
      };

      // Prepare headers with idempotency key
      const headers = {
        ...this.getAuthHeaders(),
        'X-Idempotency-Key': this.generateIdempotencyKey()
      };

      const response = await fetch(`${this.baseURL}${this.endpoints.submitAssessment}`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Store job information for tracking
        localStorage.setItem('currentJobId', result.data.jobId);
        localStorage.setItem('assessmentSubmitted', 'true');
        localStorage.setItem('assessmentResults', JSON.stringify(assessmentData));

        return {
          success: true,
          data: result.data,
          message: 'Assessment submitted successfully for processing'
        };
      } else {
        throw new Error(result.message || 'Assessment submission failed');
      }

    } catch (error) {
      console.error('Error submitting assessment:', error);

      // Fallback: save to localStorage if API fails
      const fallbackData = {
        ...assessmentData,
        id: `local_${Date.now()}`,
        savedAt: new Date().toISOString(),
        source: 'localStorage'
      };

      localStorage.setItem('assessmentResults', JSON.stringify(fallbackData));
      localStorage.setItem('assessmentSubmitted', 'false');

      return {
        success: false,
        error: error.message,
        fallbackSaved: true,
        data: fallbackData,
        message: 'Assessment saved locally due to connection issues'
      };
    }
  }

  // Check assessment status by job ID
  async checkAssessmentStatus(jobId) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getAssessmentStatus}/${jobId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('Error checking assessment status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get queue status
  async getQueueStatus() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getQueueStatus}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('Error getting queue status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get job status
  async getJobStatus(jobId) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getJobStatus}/${jobId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data
      };

    } catch (error) {
      console.error('Error getting job status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get specific result by ID
  async getResult(resultId) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getResult}/${resultId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.data.status === 'completed') {
        // Parse the persona profile from the result
        const personaProfile = JSON.parse(result.data.persona_profile);

        // Store the results locally
        localStorage.setItem('personaProfile', JSON.stringify(personaProfile));
        localStorage.setItem('assessmentResultReady', 'true');

        return {
          success: true,
          data: {
            ...result.data,
            persona_profile: personaProfile
          }
        };
      }

      return {
        success: true,
        data: result.data
      };

    } catch (error) {
      console.error('Error getting result:', error);

      // Fallback: try to get from localStorage
      const localProfile = localStorage.getItem('personaProfile');
      if (localProfile) {
        return {
          success: true,
          data: {
            persona_profile: JSON.parse(localProfile),
            status: 'completed'
          },
          source: 'localStorage'
        };
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get all assessments for current user
  async getUserAssessments() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getUserAssessments}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('Error fetching user assessments:', error);
      
      // Fallback: return local assessment if available
      const localData = localStorage.getItem('assessmentResults');
      if (localData) {
        return {
          success: true,
          data: [JSON.parse(localData)],
          source: 'localStorage'
        };
      }
      
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  // Retry submitting assessment (for offline scenarios)
  async retrySubmitAssessment() {
    const localData = localStorage.getItem('assessmentResults');
    if (!localData) {
      return { success: false, message: 'No local assessment data found' };
    }

    const assessmentData = JSON.parse(localData);
    if (assessmentData.source !== 'localStorage') {
      return { success: false, message: 'Assessment already submitted to server' };
    }

    // Remove local-specific fields before sending to server
    const { id, savedAt, source, ...serverData } = assessmentData;

    return await this.submitAssessment(serverData);
  }

  // Check if there are unsubmitted local assessments
  hasUnsubmittedAssessments() {
    const localData = localStorage.getItem('assessmentResults');
    if (!localData) return false;

    const assessmentData = JSON.parse(localData);
    return assessmentData.source === 'localStorage';
  }

  // Check if assessment is currently being processed
  isAssessmentProcessing() {
    const jobId = localStorage.getItem('currentJobId');
    const submitted = localStorage.getItem('assessmentSubmitted');
    const ready = localStorage.getItem('assessmentResultReady');

    return jobId && submitted === 'true' && ready !== 'true';
  }

  // Get current job ID
  getCurrentJobId() {
    return localStorage.getItem('currentJobId');
  }

  // Clear local assessment data
  clearLocalAssessmentData() {
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('currentJobId');
    localStorage.removeItem('assessmentSubmitted');
    localStorage.removeItem('assessmentResultReady');
    localStorage.removeItem('personaProfile');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
    localStorage.removeItem('lastSaveResult');
  }
}

// Create singleton instance
export const assessmentService = new AssessmentService();
