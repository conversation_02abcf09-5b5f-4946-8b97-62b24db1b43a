// Authentication Service Test Utility
// Use this to test the auth API integration

import { authService } from '../services/authService.js';

// Test functions for auth service
export class AuthTest {
  
  // Test user registration
  static async testRegister(email = '<EMAIL>', password = 'testPassword123') {
    console.log('🧪 Testing user registration...');
    console.log('Email:', email);
    console.log('Password:', password);
    
    try {
      const result = await authService.register(email, password);
      console.log('✅ Register result:', result);
      return result;
    } catch (error) {
      console.error('❌ Register error:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Test user login
  static async testLogin(email = '<EMAIL>', password = 'testPassword123') {
    console.log('🧪 Testing user login...');
    console.log('Email:', email);
    console.log('Password:', password);
    
    try {
      const result = await authService.login(email, password);
      console.log('✅ Login result:', result);
      return result;
    } catch (error) {
      console.error('❌ Login error:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Test get token balance
  static async testGetTokenBalance() {
    console.log('🧪 Testing get token balance...');
    
    try {
      const result = await authService.getTokenBalance();
      console.log('✅ Token balance result:', result);
      return result;
    } catch (error) {
      console.error('❌ Token balance error:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Test change password
  static async testChangePassword(currentPassword = 'testPassword123', newPassword = 'newPassword456') {
    console.log('🧪 Testing change password...');
    console.log('Current password:', currentPassword);
    console.log('New password:', newPassword);
    
    try {
      const result = await authService.changePassword(currentPassword, newPassword);
      console.log('✅ Change password result:', result);
      return result;
    } catch (error) {
      console.error('❌ Change password error:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Test logout
  static async testLogout() {
    console.log('🧪 Testing logout...');
    
    try {
      const result = await authService.logout();
      console.log('✅ Logout result:', result);
      return result;
    } catch (error) {
      console.error('❌ Logout error:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Test delete account
  static async testDeleteAccount() {
    console.log('🧪 Testing delete account...');
    console.log('⚠️ WARNING: This will delete the test account!');
    
    try {
      const result = await authService.deleteAccount();
      console.log('✅ Delete account result:', result);
      return result;
    } catch (error) {
      console.error('❌ Delete account error:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Run full authentication flow test
  static async runFullTest() {
    console.log('🚀 Starting full authentication test...');
    
    const testEmail = `test_${Date.now()}@example.com`;
    const testPassword = 'testPassword123';
    const newPassword = 'newPassword456';
    
    try {
      // 1. Test registration
      console.log('\n1️⃣ Testing Registration...');
      const registerResult = await this.testRegister(testEmail, testPassword);
      if (!registerResult.success) {
        console.log('❌ Registration failed, stopping test');
        return;
      }
      
      // 2. Test token balance
      console.log('\n2️⃣ Testing Token Balance...');
      await this.testGetTokenBalance();
      
      // 3. Test change password
      console.log('\n3️⃣ Testing Change Password...');
      await this.testChangePassword(testPassword, newPassword);
      
      // 4. Test login with new password
      console.log('\n4️⃣ Testing Login with New Password...');
      await this.testLogin(testEmail, newPassword);
      
      // 5. Test logout
      console.log('\n5️⃣ Testing Logout...');
      await this.testLogout();
      
      // 6. Test login again
      console.log('\n6️⃣ Testing Login Again...');
      await this.testLogin(testEmail, newPassword);
      
      // 7. Test delete account (optional - uncomment if you want to test)
      // console.log('\n7️⃣ Testing Delete Account...');
      // await this.testDeleteAccount();
      
      console.log('\n✅ Full authentication test completed!');
      
    } catch (error) {
      console.error('❌ Full test error:', error);
    }
  }
  
  // Check current authentication status
  static checkAuthStatus() {
    console.log('🔍 Checking authentication status...');
    console.log('Is authenticated:', authService.isAuthenticated());
    console.log('Current user:', authService.getCurrentUser());
    console.log('Current token:', authService.getCurrentToken());
  }
  
  // Clear authentication data
  static clearAuth() {
    console.log('🧹 Clearing authentication data...');
    authService.clearAuthData();
    console.log('✅ Authentication data cleared');
  }
}

// Make test functions available globally for console testing
window.AuthTest = AuthTest;

// Quick test functions
window.testAuth = {
  register: (email, password) => AuthTest.testRegister(email, password),
  login: (email, password) => AuthTest.testLogin(email, password),
  tokenBalance: () => AuthTest.testGetTokenBalance(),
  changePassword: (current, newPass) => AuthTest.testChangePassword(current, newPass),
  logout: () => AuthTest.testLogout(),
  deleteAccount: () => AuthTest.testDeleteAccount(),
  fullTest: () => AuthTest.runFullTest(),
  status: () => AuthTest.checkAuthStatus(),
  clear: () => AuthTest.clearAuth()
};

console.log('🧪 Auth test utility loaded!');
console.log('Use window.testAuth or window.AuthTest to test authentication features');
console.log('Example: testAuth.fullTest() or testAuth.login("<EMAIL>", "password123")');
