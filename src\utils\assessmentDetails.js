// Assessment Details and Interpretations
// Comprehensive information about RIASEC, OCEAN, and ViaIS assessments

export const RIASEC_DETAILS = {
  realistic: {
    name: 'Realistic (R) - The Doers',
    description: 'Orang dengan tipe Realistic menyukai aktivitas praktis dan hands-on. Mereka cenderung bekerja dengan alat, mesin, atau di lingkungan outdoor.',
    characteristics: [
      'Praktis dan suka bekerja dengan tangan',
      'Menyukai aktivitas fisik dan outdoor',
      '<PERSON><PERSON>h suka hasil yang konkret dan terukur',
      'Cenderung langsung to-the-point',
      'Menyukai lingkungan kerja yang terstruktur'
    ],
    careerExamples: [
      'Teknisi', 'Mekanik', 'Insinyur', 'Ars<PERSON>k', '<PERSON>',
      '<PERSON><PERSON>', 'Tukang <PERSON>u', '<PERSON><PERSON>', 'Chef', 'Do<PERSON><PERSON>'
    ],
    workEnvironment: [
      'Workshop dan laboratorium',
      'Lapangan dan outdoor',
      'Pabrik dan fasilitas produksi',
      '<PERSON><PERSON><PERSON> sakit dan klinik',
      'Pertanian dan peternakan'
    ],
    strengths: ['Problem solving praktis', 'Keterampilan teknis', '<PERSON><PERSON><PERSON>an', 'Ketahanan fisik'],
    developmentAreas: ['Komunikasi interpersonal', 'Presentasi', 'Networking', 'Manajemen tim']
  },
  
  investigative: {
    name: 'Investigative (I) - The Thinkers',
    description: 'Orang dengan tipe Investigative menyukai aktivitas yang melibatkan penelitian, analisis, dan pemecahan masalah kompleks.',
    characteristics: [
      'Analitis dan logis dalam berpikir',
      'Menyukai penelitian dan eksplorasi',
      'Tertarik pada teori dan konsep abstrak',
      'Independen dan self-directed',
      'Detail-oriented dan metodis'
    ],
    careerExamples: [
      'Peneliti', 'Ilmuwan', 'Analis Data', 'Psikolog', 'Dokter',
      'Programmer', 'Ekonom', 'Matematikawan', 'Ahli Forensik', 'Akademisi'
    ],
    workEnvironment: [
      'Laboratorium penelitian',
      'Universitas dan institusi pendidikan',
      'Rumah sakit dan klinik',
      'Perusahaan teknologi',
      'Lembaga riset pemerintah'
    ],
    strengths: ['Analytical thinking', 'Research skills', 'Problem solving', 'Attention to detail'],
    developmentAreas: ['Leadership skills', 'Sales abilities', 'Public speaking', 'Team collaboration']
  },

  artistic: {
    name: 'Artistic (A) - The Creators',
    description: 'Orang dengan tipe Artistic menyukai aktivitas kreatif dan ekspresif. Mereka cenderung inovatif dan menyukai kebebasan berekspresi.',
    characteristics: [
      'Kreatif dan imajinatif',
      'Ekspresif dan emosional',
      'Menyukai kebebasan dan fleksibilitas',
      'Intuitif dan spontan',
      'Menghargai keindahan dan estetika'
    ],
    careerExamples: [
      'Desainer Grafis', 'Seniman', 'Penulis', 'Musisi', 'Aktor',
      'Fotografer', 'Animator', 'Interior Designer', 'Fashion Designer', 'Content Creator'
    ],
    workEnvironment: [
      'Studio kreatif',
      'Agensi periklanan',
      'Media dan entertainment',
      'Galeri dan museum',
      'Freelance dan remote work'
    ],
    strengths: ['Creativity', 'Innovation', 'Aesthetic sense', 'Flexibility'],
    developmentAreas: ['Business skills', 'Time management', 'Financial planning', 'Networking']
  },

  social: {
    name: 'Social (S) - The Helpers',
    description: 'Orang dengan tipe Social menyukai aktivitas yang melibatkan membantu, mengajar, dan berinteraksi dengan orang lain.',
    characteristics: [
      'Empati dan peduli terhadap orang lain',
      'Komunikatif dan interpersonal',
      'Menyukai kerja tim dan kolaborasi',
      'Motivasi untuk membantu dan melayani',
      'Responsif terhadap kebutuhan orang lain'
    ],
    careerExamples: [
      'Guru', 'Konselor', 'Perawat', 'Pekerja Sosial', 'HR Manager',
      'Terapis', 'Customer Service', 'Event Organizer', 'Trainer', 'Diplomat'
    ],
    workEnvironment: [
      'Sekolah dan universitas',
      'Rumah sakit dan klinik',
      'Organisasi non-profit',
      'Perusahaan dengan fokus people',
      'Komunitas dan pemerintahan'
    ],
    strengths: ['Interpersonal skills', 'Empathy', 'Communication', 'Team building'],
    developmentAreas: ['Technical skills', 'Data analysis', 'Financial management', 'Strategic thinking']
  },

  enterprising: {
    name: 'Enterprising (E) - The Persuaders',
    description: 'Orang dengan tipe Enterprising menyukai aktivitas yang melibatkan kepemimpinan, persuasi, dan pencapaian tujuan bisnis.',
    characteristics: [
      'Ambisius dan goal-oriented',
      'Persuasif dan influential',
      'Menyukai tantangan dan kompetisi',
      'Berorientasi pada hasil dan profit',
      'Confident dan assertive'
    ],
    careerExamples: [
      'Entrepreneur', 'Sales Manager', 'Marketing Director', 'CEO', 'Lawyer',
      'Politician', 'Real Estate Agent', 'Investment Banker', 'Consultant', 'Business Developer'
    ],
    workEnvironment: [
      'Perusahaan dan korporasi',
      'Startup dan bisnis',
      'Firma hukum',
      'Lembaga keuangan',
      'Organisasi politik'
    ],
    strengths: ['Leadership', 'Persuasion', 'Business acumen', 'Risk taking'],
    developmentAreas: ['Technical expertise', 'Patience', 'Detail orientation', 'Research skills']
  },

  conventional: {
    name: 'Conventional (C) - The Organizers',
    description: 'Orang dengan tipe Conventional menyukai aktivitas yang terstruktur, terorganisir, dan mengikuti prosedur yang jelas.',
    characteristics: [
      'Terorganisir dan sistematis',
      'Detail-oriented dan akurat',
      'Menyukai struktur dan prosedur',
      'Reliable dan dapat diandalkan',
      'Efisien dalam mengelola data'
    ],
    careerExamples: [
      'Akuntan', 'Administrator', 'Sekretaris', 'Auditor', 'Banker',
      'Data Analyst', 'Librarian', 'Quality Control', 'Project Coordinator', 'Bookkeeper'
    ],
    workEnvironment: [
      'Kantor dan perkantoran',
      'Bank dan lembaga keuangan',
      'Pemerintahan',
      'Perusahaan besar',
      'Organisasi dengan struktur jelas'
    ],
    strengths: ['Organization', 'Attention to detail', 'Reliability', 'Efficiency'],
    developmentAreas: ['Creativity', 'Innovation', 'Risk taking', 'Interpersonal skills']
  }
};

export const OCEAN_DETAILS = {
  openness: {
    name: 'Openness to Experience (Keterbukaan)',
    description: 'Mengukur seberapa terbuka seseorang terhadap pengalaman baru, ide-ide kreatif, dan hal-hal yang tidak konvensional.',
    highScore: {
      characteristics: [
        'Kreatif dan imajinatif',
        'Menyukai hal-hal baru dan berbeda',
        'Terbuka terhadap ide dan pengalaman baru',
        'Menyukai seni dan keindahan',
        'Fleksibel dalam berpikir'
      ],
      implications: [
        'Cocok untuk pekerjaan kreatif',
        'Mudah beradaptasi dengan perubahan',
        'Menyukai tantangan intelektual',
        'Tertarik pada budaya dan filosofi'
      ]
    },
    lowScore: {
      characteristics: [
        'Praktis dan realistis',
        'Menyukai rutinitas dan hal yang familiar',
        'Konservatif dalam pendekatan',
        'Fokus pada hal-hal konkret',
        'Stabil dan dapat diprediksi'
      ],
      implications: [
        'Cocok untuk pekerjaan yang terstruktur',
        'Reliable dalam tugas rutin',
        'Menyukai lingkungan yang stabil',
        'Fokus pada efisiensi dan praktikalitas'
      ]
    }
  },

  conscientiousness: {
    name: 'Conscientiousness (Kehati-hatian)',
    description: 'Mengukur seberapa terorganisir, disiplin, dan bertanggung jawab seseorang dalam menjalankan tugas.',
    highScore: {
      characteristics: [
        'Terorganisir dan sistematis',
        'Disiplin dan bertanggung jawab',
        'Berorientasi pada tujuan',
        'Teliti dan detail-oriented',
        'Dapat diandalkan'
      ],
      implications: [
        'Performa kerja yang konsisten',
        'Cocok untuk posisi leadership',
        'Sukses dalam pendidikan dan karir',
        'Dipercaya dalam tanggung jawab besar'
      ]
    },
    lowScore: {
      characteristics: [
        'Fleksibel dan spontan',
        'Santai dalam pendekatan',
        'Kreatif dalam problem solving',
        'Adaptif terhadap perubahan',
        'Menyukai kebebasan'
      ],
      implications: [
        'Cocok untuk pekerjaan kreatif',
        'Mudah beradaptasi',
        'Menyukai lingkungan yang dinamis',
        'Perlu struktur eksternal untuk produktivitas'
      ]
    }
  },

  extraversion: {
    name: 'Extraversion (Ekstraversi)',
    description: 'Mengukur seberapa energik, asertif, dan sosial seseorang dalam berinteraksi dengan dunia luar.',
    highScore: {
      characteristics: [
        'Energik dan antusias',
        'Sosial dan mudah bergaul',
        'Asertif dan percaya diri',
        'Menyukai perhatian dan interaksi',
        'Optimis dan positif'
      ],
      implications: [
        'Cocok untuk pekerjaan yang melibatkan orang',
        'Natural leader dan presenter',
        'Sukses dalam sales dan marketing',
        'Menyukai lingkungan kerja yang dinamis'
      ]
    },
    lowScore: {
      characteristics: [
        'Tenang dan reserved',
        'Menyukai kesendirian',
        'Thoughtful dan reflektif',
        'Lebih suka mendengar daripada berbicara',
        'Fokus dan konsentrasi tinggi'
      ],
      implications: [
        'Cocok untuk pekerjaan yang membutuhkan konsentrasi',
        'Excellent dalam research dan analysis',
        'Menyukai lingkungan kerja yang tenang',
        'Produktif dalam kerja individual'
      ]
    }
  },

  agreeableness: {
    name: 'Agreeableness (Keramahan)',
    description: 'Mengukur seberapa kooperatif, empati, dan peduli seseorang terhadap orang lain.',
    highScore: {
      characteristics: [
        'Empati dan peduli',
        'Kooperatif dan supportive',
        'Mudah memaafkan',
        'Altruistik dan helpful',
        'Menghindari konflik'
      ],
      implications: [
        'Excellent dalam teamwork',
        'Cocok untuk pekerjaan helping professions',
        'Dipercaya sebagai mediator',
        'Sukses dalam customer service'
      ]
    },
    lowScore: {
      characteristics: [
        'Kompetitif dan asertif',
        'Objektif dalam keputusan',
        'Skeptis dan kritis',
        'Fokus pada hasil',
        'Tidak mudah terpengaruh'
      ],
      implications: [
        'Cocok untuk posisi leadership',
        'Excellent dalam negotiation',
        'Sukses dalam bisnis dan entrepreneurship',
        'Efektif dalam decision making'
      ]
    }
  },

  neuroticism: {
    name: 'Neuroticism (Neurotisisme)',
    description: 'Mengukur seberapa stabil emosi seseorang dan kemampuan mengatasi stress.',
    highScore: {
      characteristics: [
        'Sensitif terhadap stress',
        'Emosional dan reactive',
        'Cenderung cemas dan khawatir',
        'Mood yang berubah-ubah',
        'Perfeksionis'
      ],
      implications: [
        'Perlu lingkungan kerja yang supportive',
        'Benefit dari stress management',
        'Mungkin perlu work-life balance yang baik',
        'Cocok untuk pekerjaan dengan struktur jelas'
      ]
    },
    lowScore: {
      characteristics: [
        'Stabil secara emosional',
        'Tenang dalam menghadapi tekanan',
        'Resilient dan adaptif',
        'Optimis dan positive',
        'Self-confident'
      ],
      implications: [
        'Excellent dalam high-pressure situations',
        'Natural crisis manager',
        'Cocok untuk leadership roles',
        'Sukses dalam pekerjaan yang menantang'
      ]
    }
  }
};

// ViaIS Categories with detailed information
export const VIA_IS_CATEGORIES = {
  wisdom: {
    name: 'Wisdom & Knowledge',
    description: 'Kekuatan kognitif yang melibatkan akuisisi dan penggunaan pengetahuan',
    icon: '🧠',
    color: 'blue',
    strengths: ['creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective'],
    details: 'Kategori ini mencakup kekuatan yang berhubungan dengan cara kita memperoleh dan menggunakan pengetahuan.'
  },
  courage: {
    name: 'Courage',
    description: 'Kekuatan emosional yang melibatkan pelaksanaan kemauan untuk mencapai tujuan',
    icon: '💪',
    color: 'red',
    strengths: ['bravery', 'perseverance', 'honesty', 'zest'],
    details: 'Kategori ini mencakup kekuatan yang membantu kita menghadapi tantangan dan mencapai tujuan.'
  },
  humanity: {
    name: 'Humanity',
    description: 'Kekuatan interpersonal yang melibatkan merawat dan berteman dengan orang lain',
    icon: '❤️',
    color: 'pink',
    strengths: ['love', 'kindness', 'socialIntelligence'],
    details: 'Kategori ini mencakup kekuatan yang membantu kita membangun hubungan yang bermakna.'
  },
  justice: {
    name: 'Justice',
    description: 'Kekuatan civic yang mendasari kehidupan komunitas yang sehat',
    icon: '⚖️',
    color: 'green',
    strengths: ['teamwork', 'fairness', 'leadership'],
    details: 'Kategori ini mencakup kekuatan yang membantu kita berkontribusi pada masyarakat.'
  },
  temperance: {
    name: 'Temperance',
    description: 'Kekuatan yang melindungi dari kelebihan dan membantu moderasi',
    icon: '🎯',
    color: 'purple',
    strengths: ['forgiveness', 'humility', 'prudence', 'selfRegulation'],
    details: 'Kategori ini mencakup kekuatan yang membantu kita mengendalikan diri dan bertindak bijaksana.'
  },
  transcendence: {
    name: 'Transcendence',
    description: 'Kekuatan yang menghubungkan dengan alam semesta yang lebih luas dan memberikan makna',
    icon: '✨',
    color: 'yellow',
    strengths: ['appreciationOfBeauty', 'gratitude', 'hope', 'humor', 'spirituality'],
    details: 'Kategori ini mencakup kekuatan yang membantu kita menemukan makna dan tujuan hidup.'
  }
};

// ViaIS Strength Details
export const VIA_IS_STRENGTH_DETAILS = {
  creativity: { name: 'Creativity', description: 'Kemampuan menghasilkan ide-ide baru dan original' },
  curiosity: { name: 'Curiosity', description: 'Minat yang kuat untuk terus belajar dan mengeksplorasi' },
  judgment: { name: 'Judgment', description: 'Kemampuan berpikir kritis dan membuat keputusan yang bijak' },
  loveOfLearning: { name: 'Love of Learning', description: 'Passion untuk terus mengembangkan pengetahuan dan keterampilan' },
  perspective: { name: 'Perspective', description: 'Kemampuan melihat gambaran besar dan memberikan nasihat bijak' },
  bravery: { name: 'Bravery', description: 'Kemampuan menghadapi tantangan dan ketakutan' },
  perseverance: { name: 'Perseverance', description: 'Kemampuan bertahan dan tidak menyerah dalam menghadapi kesulitan' },
  honesty: { name: 'Honesty', description: 'Komitmen untuk selalu berkata dan bertindak jujur' },
  zest: { name: 'Zest', description: 'Energi dan antusiasme dalam menjalani hidup' },
  love: { name: 'Love', description: 'Kemampuan mencintai dan dicintai dalam hubungan yang dekat' },
  kindness: { name: 'Kindness', description: 'Kemurahan hati dan kepedulian terhadap orang lain' },
  socialIntelligence: { name: 'Social Intelligence', description: 'Kemampuan memahami dan berinteraksi dengan orang lain' },
  teamwork: { name: 'Teamwork', description: 'Kemampuan bekerja sama dan berkontribusi dalam tim' },
  fairness: { name: 'Fairness', description: 'Komitmen untuk memperlakukan semua orang dengan adil' },
  leadership: { name: 'Leadership', description: 'Kemampuan memimpin dan menginspirasi orang lain' },
  forgiveness: { name: 'Forgiveness', description: 'Kemampuan memaafkan kesalahan orang lain' },
  humility: { name: 'Humility', description: 'Kemampuan tetap rendah hati meski memiliki pencapaian' },
  prudence: { name: 'Prudence', description: 'Kemampuan membuat pilihan yang bijaksana dan hati-hati' },
  selfRegulation: { name: 'Self-Regulation', description: 'Kemampuan mengendalikan emosi dan perilaku' },
  appreciationOfBeauty: { name: 'Appreciation of Beauty', description: 'Kemampuan menghargai keindahan dan keunggulan' },
  gratitude: { name: 'Gratitude', description: 'Kemampuan menghargai hal-hal baik dalam hidup' },
  hope: { name: 'Hope', description: 'Optimisme dan keyakinan akan masa depan yang baik' },
  humor: { name: 'Humor', description: 'Kemampuan melihat sisi lucu dan membawa kegembiraan' },
  spirituality: { name: 'Spirituality', description: 'Rasa terhubung dengan sesuatu yang lebih besar' }
};
