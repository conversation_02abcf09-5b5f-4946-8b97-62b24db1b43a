@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Futuristic Styles for PetaTalenta */
@layer base {
  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    margin: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: #e2e8f0;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.01em;
  }

  #app {
    min-height: 100vh;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(15, 15, 35, 0.3);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #64748b, #475569);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #94a3b8, #64748b);
  }
}

@layer components {
  /* Premium Glass Morphism Cards */
  .glass-card {
    @apply backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card-light {
    @apply backdrop-blur-xl bg-white/95 border border-gray-200/50 rounded-2xl shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  /* Modern Buttons */
  .btn-primary {
    @apply relative px-6 py-3 rounded-xl font-medium text-white transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  .btn-primary:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
  }

  .btn-secondary {
    @apply relative px-6 py-3 rounded-xl font-medium text-gray-700 bg-white/90 backdrop-blur-sm border border-gray-200/50 transition-all duration-300 transform hover:scale-105 hover:bg-white hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
  }

  .btn-ghost {
    @apply relative px-6 py-3 rounded-xl font-medium text-gray-300 bg-transparent border border-gray-600/30 transition-all duration-300 hover:bg-white/5 hover:border-gray-500/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
  }

  /* Premium Navigation */
  .nav-glass {
    @apply backdrop-blur-2xl bg-white/10 border-b border-white/10;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  }

  /* Futuristic Progress Bars */
  .progress-bar-modern {
    @apply w-full h-2 rounded-full overflow-hidden;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  }

  .progress-fill-modern {
    @apply h-full rounded-full transition-all duration-700 ease-out;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
  }

  /* Metric Cards */
  .metric-card {
    @apply glass-card p-6 hover:bg-white/10 transition-all duration-300 group;
  }

  .metric-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Data Visualization Containers */
  .chart-container {
    @apply glass-card p-8 relative overflow-hidden;
  }

  .chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  }

  /* Typography Enhancements */
  .heading-primary {
    @apply text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent;
    font-weight: 700;
    letter-spacing: -0.02em;
  }

  .heading-secondary {
    @apply text-2xl md:text-3xl font-semibold text-gray-100;
    font-weight: 600;
    letter-spacing: -0.01em;
  }

  .text-premium {
    @apply text-gray-300 leading-relaxed;
    font-weight: 400;
  }

  /* Status Indicators */
  .status-high {
    @apply bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 text-emerald-300;
  }

  .status-medium {
    @apply bg-gradient-to-r from-amber-500/20 to-yellow-500/20 border border-amber-500/30 text-amber-300;
  }

  .status-low {
    @apply bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border border-blue-500/30 text-blue-300;
  }

  /* Interactive Elements */
  .interactive-card {
    @apply glass-card cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:bg-white/10;
  }

  .interactive-card:hover {
    box-shadow:
      0 16px 48px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Form Elements */
  .form-input-modern {
    @apply w-full px-4 py-3 rounded-xl bg-white/5 border border-white/10 text-gray-200 placeholder-gray-400 backdrop-blur-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 focus:bg-white/10;
  }
}

@layer utilities {
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .backdrop-blur-strong {
    backdrop-filter: blur(20px);
  }
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
  }
}

/* Light theme overrides for result pages */
.light-theme {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  color: #1e293b;
}

.light-theme .glass-card {
  @apply glass-card-light;
}

.light-theme .text-premium {
  @apply text-gray-600;
}

.light-theme .heading-primary {
  @apply bg-gradient-to-r from-gray-900 via-gray-700 to-gray-600 bg-clip-text text-transparent;
}

.light-theme .heading-secondary {
  @apply text-gray-800;
}