// Import utilities for RIASEC assessment details and charts
import { RIASEC_DETAILS } from '../utils/assessmentDetails.js';
import { ChartUtils } from '../utils/chartUtils.js';

export function createResultRiasecPage() {
  return `
    <div class="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50">
      <!-- Futuristic Navigation -->
      <nav class="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-white/80 border-b border-gray-200/60">
        <div class="max-w-7xl mx-auto px-6 py-4">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-8">
              <button onclick="navigateTo('result-via-is')"
                class="group flex items-center text-gray-500 hover:text-gray-900 transition-all duration-300">
                <div class="w-8 h-8 mr-3 rounded-full bg-gray-100 group-hover:bg-gray-200 flex items-center justify-center transition-all duration-300">
                  <svg class="w-4 h-4 group-hover:-translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                </div>
                <span class="font-medium tracking-wide">VIA-IS</span>
              </button>
              <div class="h-4 w-px bg-gray-300"></div>
              <h1 class="text-lg font-semibold text-gray-900 tracking-wide">RIASEC Career Interests</h1>
            </div>
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-3 px-4 py-2 rounded-full bg-blue-50 border border-blue-100">
                <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-blue-700">Complete</span>
              </div>
              <button onclick="navigateTo('result-ocean')"
                class="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-full font-medium hover:from-blue-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                Next: OCEAN
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Progress Indicator -->
      <div class="pt-20 pb-8">
        <div class="max-w-7xl mx-auto px-6">
          <div class="relative">
            <div class="h-1 bg-gradient-to-r from-gray-200 via-gray-200 to-gray-200 rounded-full overflow-hidden">
              <div class="h-full bg-gradient-to-r from-blue-400 via-cyan-500 to-teal-600 rounded-full transition-all duration-1000 ease-out" style="width: 60%"></div>
            </div>
            <div class="absolute -top-2 right-2/5 transform translate-x-2">
              <div class="w-5 h-5 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-6xl mx-auto px-6 pb-12">
        <!-- Hero Section -->
        <div class="relative mb-12">
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl p-10 border border-gray-200/60 shadow-xl hover:shadow-2xl transition-all duration-500">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-cyan-500/5 to-teal-500/5 rounded-3xl"></div>
            <div class="relative z-10">
              <div class="flex items-start space-x-8">
                <div class="flex-shrink-0">
                  <div class="w-28 h-28 bg-gradient-to-br from-blue-500 via-cyan-600 to-teal-700 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <span class="text-5xl relative z-10">🎯</span>
                  </div>
                </div>
                <div class="flex-1">
                  <h1 class="text-4xl font-bold text-gray-900 mb-4 tracking-tight">RIASEC Holland Codes</h1>
                  <p class="text-gray-600 text-lg leading-relaxed mb-8 max-w-3xl">
                    Holland's theory identifies 6 work personality types that help determine career interests and work environments
                    that align perfectly with your natural preferences and professional aspirations.
                  </p>
                  <div class="flex flex-wrap gap-4">
                    <div class="px-5 py-2.5 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-full border border-blue-100">
                      <span class="text-sm font-medium text-blue-700">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Career Focused
                      </span>
                    </div>
                    <div class="px-5 py-2.5 bg-gradient-to-r from-cyan-50 to-teal-50 rounded-full border border-cyan-100">
                      <span class="text-sm font-medium text-cyan-700">
                        <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        Industry Standard
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- RIASEC Explanation -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">About RIASEC Holland Codes</h3>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">What is RIASEC?</h4>
              <div class="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  RIASEC is a theory developed by psychologist John Holland that classifies
                  personality and work environments into 6 main types.
                </p>
                <p>
                  This theory is based on the premise that people will be more satisfied and successful in careers
                  that match their personality type.
                </p>
                <p>
                  RIASEC stands for: <strong>R</strong>ealistic, <strong>I</strong>nvestigative,
                  <strong>A</strong>rtistic, <strong>S</strong>ocial, <strong>E</strong>nterprising, and <strong>C</strong>onventional.
                </p>
              </div>
            </div>

            <div>
              <h4 class="text-lg font-medium text-gray-900 mb-4">Benefits of Knowing Your RIASEC Type</h4>
              <div class="space-y-3">
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Helps with proper career selection</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Increases job satisfaction</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Understand ideal work environment</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Identify strengths and preferences</span>
                </div>
                <div class="flex items-start space-x-3">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <span class="text-gray-700 leading-relaxed">Guidance for career development</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- RIASEC Types Overview -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">6 RIASEC Personality Types</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="riasec-types-overview">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- User's RIASEC Profile -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Radar Chart -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900">Radar Chart Visualization</h4>
            </div>
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <canvas id="riasecRadarChart" width="400" height="400"></canvas>
            </div>
          </div>

          <!-- Top 3 Types -->
          <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-teal-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                </svg>
              </div>
              <h4 class="text-xl font-semibold text-gray-900">Your Top 3 Personality Types</h4>
            </div>
            <div id="top-riasec-types" class="space-y-4">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Detailed RIASEC Results -->
        <div class="bg-white/70 backdrop-blur-xl rounded-2xl p-8 border border-gray-200/60 shadow-lg mb-12">
          <div class="flex items-center mb-8">
            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900">Detailed RIASEC Assessment Results</h3>
          </div>
          <div id="riasec-detailed-results" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Will be populated by JavaScript -->
          </div>
        </div>

        <!-- Final Navigation -->
        <div class="flex justify-between items-center">
          <button onclick="navigateTo('result-via-is')"
            class="group flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all duration-300 border border-gray-200 hover:border-gray-300">
            <svg class="w-5 h-5 mr-3 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            <span class="font-medium">Back to VIA-IS</span>
          </button>
          <button onclick="navigateTo('result-ocean')"
            class="group flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <span class="font-medium mr-3">Continue to OCEAN</span>
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  `;
}

export function initResultRiasec() {
  // Load RIASEC assessment data
  loadRiasecData();
}

function loadRiasecData() {
  try {
    // Load assessment results
    const savedResults = localStorage.getItem('assessmentResults');
    const assessmentResults = savedResults ? JSON.parse(savedResults) : getSampleRiasecData();

    console.log('RIASEC assessment results loaded:', assessmentResults);

    displayRiasecData(assessmentResults.riasec || assessmentResults);
  } catch (error) {
    console.error('Error loading RIASEC data:', error);
    // Fallback to sample data
    displayRiasecData(getSampleRiasecData());
  }
}

function getSampleRiasecData() {
  return {
    realistic: 25,
    investigative: 92,
    artistic: 78,
    social: 45,
    enterprising: 55,
    conventional: 68
  };
}

function displayRiasecData(riasecData) {
  // Display types overview
  displayTypesOverview();

  // Create radar chart
  createRiasecRadarChart(riasecData);

  // Display top 3 types
  displayTopTypes(riasecData);

  // Display detailed results
  displayDetailedResults(riasecData);
}

function displayTypesOverview() {
  const container = document.getElementById('riasec-types-overview');
  if (!container) return;

  const riasecTypes = [
    {
      code: 'R',
      name: 'Realistic',
      description: 'Praktis, hands-on, suka bekerja dengan alat dan mesin',
      color: 'bg-red-50 border-red-200 text-red-800',
      icon: '🔧'
    },
    {
      code: 'I',
      name: 'Investigative',
      description: 'Analitis, suka memecahkan masalah dan penelitian',
      color: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: '🔬'
    },
    {
      code: 'A',
      name: 'Artistic',
      description: 'Kreatif, ekspresif, suka seni dan inovasi',
      color: 'bg-purple-50 border-purple-200 text-purple-800',
      icon: '🎨'
    },
    {
      code: 'S',
      name: 'Social',
      description: 'Suka membantu, mengajar, dan bekerja dengan orang',
      color: 'bg-green-50 border-green-200 text-green-800',
      icon: '👥'
    },
    {
      code: 'E',
      name: 'Enterprising',
      description: 'Ambisius, suka memimpin dan berbisnis',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: '💼'
    },
    {
      code: 'C',
      name: 'Conventional',
      description: 'Terorganisir, detail, suka struktur dan aturan',
      color: 'bg-gray-50 border-gray-200 text-gray-800',
      icon: '📊'
    }
  ];

  container.innerHTML = riasecTypes.map(type => `
    <div class="border rounded-lg p-6 ${type.color}">
      <div class="flex items-center mb-4">
        <span class="text-3xl mr-4">${type.icon}</span>
        <div>
          <h4 class="font-semibold text-lg">${type.code} - ${type.name}</h4>
        </div>
      </div>
      <p class="text-sm">${type.description}</p>
    </div>
  `).join('');
}

function createRiasecRadarChart(riasecData) {
  const ctx = document.getElementById('riasecRadarChart');
  if (!ctx) return;

  return ChartUtils.createRiasecRadarChart(ctx, riasecData);
}

function displayTopTypes(riasecData) {
  const container = document.getElementById('top-riasec-types');
  if (!container) return;

  // Sort RIASEC scores to get top 3
  const sortedTypes = Object.entries(riasecData)
    .map(([key, value]) => ({
      key,
      value,
      details: RIASEC_DETAILS[key],
      name: RIASEC_DETAILS[key]?.name || key
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 3);

  const colors = [
    'bg-yellow-100 border-yellow-300 text-yellow-800',
    'bg-orange-100 border-orange-300 text-orange-800',
    'bg-red-100 border-red-300 text-red-800'
  ];

  container.innerHTML = sortedTypes.map((type, index) => `
    <div class="border-2 rounded-lg p-4 ${colors[index]}">
      <div class="flex justify-between items-center mb-3">
        <h5 class="font-semibold text-lg">#${index + 1} ${type.name}</h5>
        <span class="text-2xl font-bold">${type.value}%</span>
      </div>
      <p class="text-sm mb-3">${type.details?.description || ''}</p>
      <div class="text-xs">
        <strong>Contoh Karir:</strong> ${type.details?.careerExamples?.slice(0, 3).join(', ') || ''}
      </div>
    </div>
  `).join('');
}

function displayDetailedResults(riasecData) {
  const container = document.getElementById('riasec-detailed-results');
  if (!container) return;

  // Sort RIASEC scores to show highest first
  const sortedRiasec = Object.entries(riasecData)
    .map(([key, value]) => ({ key, value, details: RIASEC_DETAILS[key] }))
    .sort((a, b) => b.value - a.value);

  container.innerHTML = sortedRiasec.map(({ value, details }) => `
    <div class="border rounded-lg p-4 ${value >= 70 ? 'border-blue-300 bg-blue-50' : value >= 50 ? 'border-gray-300 bg-gray-50' : 'border-gray-200'}">
      <div class="flex justify-between items-center mb-3">
        <h4 class="font-semibold text-lg text-gray-900">${details.name}</h4>
        <div class="text-right">
          <div class="text-xl font-bold ${value >= 70 ? 'text-blue-600' : value >= 50 ? 'text-gray-600' : 'text-gray-400'}">${value}%</div>
          <div class="text-xs ${value >= 70 ? 'text-blue-500' : value >= 50 ? 'text-gray-500' : 'text-gray-400'}">
            ${value >= 70 ? 'Tinggi' : value >= 50 ? 'Sedang' : 'Rendah'}
          </div>
        </div>
      </div>

      <p class="text-gray-700 text-sm mb-3">${details.description}</p>

      <div class="space-y-3">
        <div>
          <h5 class="font-medium text-gray-800 mb-2 text-sm">Karakteristik:</h5>
          <ul class="text-gray-600 space-y-1 text-xs">
            ${details.characteristics.slice(0, 3).map(char => `<li class="flex items-start"><span class="text-blue-500 mr-1">•</span>${char}</li>`).join('')}
          </ul>
        </div>
        <div>
          <h5 class="font-medium text-gray-800 mb-2 text-sm">Contoh Karir:</h5>
          <div class="flex flex-wrap gap-1">
            ${details.careerExamples.slice(0, 4).map(career =>
              `<span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">${career}</span>`
            ).join('')}
          </div>
        </div>
      </div>
    </div>
  `).join('');
}


