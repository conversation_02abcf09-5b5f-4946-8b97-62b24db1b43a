// Assessment Scoring and Result Calculation Utility
// Calculates scores for Big Five, RIASEC, and VIA-IS assessments

import { assessmentParser } from './assessmentParser.js';

export class AssessmentScoring {
  constructor() {
    this.questionMapping = assessmentParser.getQuestionMapping();
    this.reverseQuestions = assessmentParser.getReverseQuestions();
  }

  // Calculate Big Five (OCEAN) scores from answers
  calculateOceanScores(answers) {
    const oceanScores = {
      openness: 0,
      conscientiousness: 0,
      extraversion: 0,
      agreeableness: 0,
      neuroticism: 0
    };

    // Calculate each dimension
    for (const [dimension, questionIds] of Object.entries(this.questionMapping.bigFive)) {
      let totalScore = 0;
      let validAnswers = 0;

      for (const questionId of questionIds) {
        if (answers[questionId] !== undefined) {
          let score = answers[questionId];
          
          // Apply reverse scoring if needed
          if (this.reverseQuestions.includes(questionId)) {
            score = 8 - score; // Reverse scale: 1->7, 2->6, 3->5, 4->4, 5->3, 6->2, 7->1
          }
          
          totalScore += score;
          validAnswers++;
        }
      }

      // Calculate average and convert to percentage (0-100)
      if (validAnswers > 0) {
        const average = totalScore / validAnswers;
        oceanScores[dimension] = Math.round(((average - 1) / 6) * 100); // Convert 1-7 scale to 0-100
      }
    }

    return oceanScores;
  }

  // Calculate RIASEC scores from answers
  calculateRiasecScores(answers) {
    const riasecScores = {
      realistic: 0,
      investigative: 0,
      artistic: 0,
      social: 0,
      enterprising: 0,
      conventional: 0
    };

    // Calculate each dimension
    for (const [dimension, questionIds] of Object.entries(this.questionMapping.riasec)) {
      let totalScore = 0;
      let validAnswers = 0;

      for (const questionId of questionIds) {
        if (answers[questionId] !== undefined) {
          totalScore += answers[questionId];
          validAnswers++;
        }
      }

      // Calculate average and convert to percentage (0-100)
      if (validAnswers > 0) {
        const average = totalScore / validAnswers;
        riasecScores[dimension] = Math.round(((average - 1) / 6) * 100); // Convert 1-7 scale to 0-100
      }
    }

    return riasecScores;
  }

  // Calculate VIA-IS character strengths scores from answers
  calculateViaIsScores(answers) {
    const viaIsScores = {
      creativity: 0,
      curiosity: 0,
      judgment: 0,
      loveOfLearning: 0,
      perspective: 0,
      bravery: 0,
      perseverance: 0,
      honesty: 0,
      zest: 0,
      love: 0,
      kindness: 0,
      socialIntelligence: 0,
      teamwork: 0,
      fairness: 0,
      leadership: 0,
      forgiveness: 0,
      humility: 0,
      prudence: 0,
      selfRegulation: 0,
      appreciationOfBeauty: 0,
      gratitude: 0,
      hope: 0,
      humor: 0,
      spirituality: 0
    };

    // Calculate each character strength
    for (const [strength, questionIds] of Object.entries(this.questionMapping.viaIs)) {
      let totalScore = 0;
      let validAnswers = 0;

      for (const questionId of questionIds) {
        if (answers[questionId] !== undefined) {
          totalScore += answers[questionId];
          validAnswers++;
        }
      }

      // Calculate average and convert to percentage (0-100)
      if (validAnswers > 0) {
        const average = totalScore / validAnswers;
        viaIsScores[strength] = Math.round(((average - 1) / 6) * 100); // Convert 1-7 scale to 0-100
      }
    }

    return viaIsScores;
  }

  // Calculate complete assessment results in the specified format
  calculateAssessmentResults(answers) {
    const oceanScores = this.calculateOceanScores(answers);
    const riasecScores = this.calculateRiasecScores(answers);
    const viaIsScores = this.calculateViaIsScores(answers);

    return {
      assessmentName: "AI-Driven Talent Mapping",
      riasec: riasecScores,
      ocean: oceanScores,
      viaIs: viaIsScores,
      completedAt: new Date().toISOString(),
      totalQuestions: Object.keys(answers).length
    };
  }

  // Validate that all required questions are answered
  validateCompleteness(answers) {
    const requiredQuestions = [
      ...Object.values(this.questionMapping.bigFive).flat(),
      ...Object.values(this.questionMapping.riasec).flat(),
      ...Object.values(this.questionMapping.viaIs).flat()
    ];

    const answeredQuestions = Object.keys(answers).map(id => parseInt(id));
    const missingQuestions = requiredQuestions.filter(id => !answeredQuestions.includes(id));

    return {
      isComplete: missingQuestions.length === 0,
      totalRequired: requiredQuestions.length,
      totalAnswered: answeredQuestions.length,
      missingQuestions: missingQuestions,
      completionPercentage: Math.round((answeredQuestions.length / requiredQuestions.length) * 100)
    };
  }
}

// Create singleton instance
export const assessmentScoring = new AssessmentScoring();
