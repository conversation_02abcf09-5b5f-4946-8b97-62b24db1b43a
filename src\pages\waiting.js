export function createWaitingPage() {
  return `
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center">
          <!-- Loading Animation -->
          <div class="mb-6">
            <div class="mx-auto w-16 h-16 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin" id="loading-spinner"></div>
          </div>

          <h2 class="text-2xl font-bold text-gray-900 mb-4" id="status-title">
            Sedang Memproses Assessment Anda
          </h2>

          <p class="text-gray-600 mb-6" id="status-description">
            AI sedang menganalisis jawaban Anda untuk memberikan hasil yang akurat dan personal.
            Proses ini biasanya memakan waktu 2-5 menit.
          </p>
          
          <!-- Progress Steps -->
          <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mb-2">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-xs text-gray-500">Assessment Selesai</span>
              </div>
              
              <div class="w-8 h-1 bg-indigo-200"></div>
              
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mb-2">
                  <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                </div>
                <span class="text-xs text-gray-500">Analisis Data</span>
              </div>
              
              <div class="w-8 h-1 bg-gray-200"></div>
              
              <div class="flex flex-col items-center">
                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mb-2">
                  <span class="text-xs text-gray-500">3</span>
                </div>
                <span class="text-xs text-gray-500">Hasil Siap</span>
              </div>
            </div>
          </div>
          
          <!-- Status Information -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6" id="status-info">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-blue-700">
                <span id="status-text">Estimasi waktu: 2-5 menit</span>
              </span>
            </div>
          </div>

          <!-- Queue Position (if applicable) -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 hidden" id="queue-info">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-yellow-700">
                Posisi antrian: <span id="queue-position">-</span>
              </span>
            </div>
          </div>
          
          <!-- Tips while waiting -->
          <div class="text-left bg-gray-50 rounded-lg p-4 mb-6">
            <h4 class="font-medium text-gray-900 mb-2">Tahukah Anda?</h4>
            <p class="text-sm text-gray-600" id="waiting-tip">
              Assessment talenta dapat membantu Anda memahami kekuatan dan area pengembangan diri.
            </p>
          </div>
          
          <!-- Action Buttons -->
          <div class="space-y-3">
            <button onclick="checkResults()" 
              class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition duration-200">
              Cek Status
            </button>
            
            <button onclick="navigateTo('dashboard')" 
              class="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-300 transition duration-200">
              Kembali ke Dashboard
            </button>
          </div>
          
          <!-- Auto-refresh notice -->
          <p class="text-xs text-gray-500 mt-4">
            Halaman ini akan otomatis memperbarui status setiap 30 detik
          </p>
        </div>
      </div>
    </div>
  `;
}

// Tips to show while waiting
const waitingTips = [
  "Assessment talenta dapat membantu Anda memahami kekuatan dan area pengembangan diri.",
  "Hasil assessment akan memberikan insight tentang gaya kerja dan preferensi karir Anda.",
  "Pemetaan talenta membantu organisasi menempatkan orang yang tepat di posisi yang tepat.",
  "Assessment ini menggunakan metodologi yang telah terbukti secara ilmiah.",
  "Hasil Anda akan dibandingkan dengan database profil talenta yang luas."
];

// Import services
import { assessmentService } from '../services/assessmentService.js';
import { websocketService } from '../services/websocketService.js';

let tipIndex = 0;
let waitingTimer;
let autoRefreshTimer;
let websocketConnected = false;

export function initWaiting() {
  // Start tip rotation
  startTipRotation();

  // Initialize WebSocket connection for real-time updates
  initializeWebSocket();

  // Start status checking
  startStatusChecking();

  // Start auto-refresh as fallback
  startAutoRefresh();
}

// Initialize WebSocket connection
async function initializeWebSocket() {
  try {
    await websocketService.connect();
    websocketConnected = true;

    // Set up event listeners for assessment updates
    websocketService.on('analysis-started', (data) => {
      updateStatus('processing', 'AI sedang menganalisis data assessment Anda...', data);
    });

    websocketService.on('analysis-complete', (data) => {
      updateStatus('completed', 'Assessment berhasil diproses!', data);
      setTimeout(() => {
        navigateTo('result');
      }, 2000);
    });

    websocketService.on('analysis-failed', (data) => {
      updateStatus('failed', 'Terjadi kesalahan dalam pemrosesan assessment.', data);
    });

    console.log('WebSocket connected for real-time updates');
  } catch (error) {
    console.warn('WebSocket connection failed, using polling fallback:', error);
    websocketConnected = false;
  }
}

// Start status checking
function startStatusChecking() {
  const jobId = assessmentService.getCurrentJobId();
  if (!jobId) {
    console.error('No job ID found');
    updateStatus('error', 'Tidak dapat menemukan informasi assessment.');
    return;
  }

  // Initial status check
  checkAssessmentStatus();
}

function startTipRotation() {
  setInterval(() => {
    tipIndex = (tipIndex + 1) % waitingTips.length;
    const tipElement = document.getElementById('waiting-tip');
    if (tipElement) {
      tipElement.textContent = waitingTips[tipIndex];
    }
  }, 10000); // Change tip every 10 seconds
}



// Update status display
function updateStatus(status, message, data = null) {
  const titleElement = document.getElementById('status-title');
  const descriptionElement = document.getElementById('status-description');
  const statusTextElement = document.getElementById('status-text');
  const queueInfoElement = document.getElementById('queue-info');
  const loadingSpinner = document.getElementById('loading-spinner');

  switch (status) {
    case 'queued':
      if (titleElement) titleElement.textContent = 'Assessment Dalam Antrian';
      if (descriptionElement) descriptionElement.textContent = message;
      if (statusTextElement) statusTextElement.textContent = `Posisi antrian: ${data?.queuePosition || 'Unknown'}`;
      if (queueInfoElement && data?.queuePosition) {
        queueInfoElement.classList.remove('hidden');
        document.getElementById('queue-position').textContent = data.queuePosition;
      }
      break;

    case 'processing':
      if (titleElement) titleElement.textContent = 'AI Sedang Menganalisis';
      if (descriptionElement) descriptionElement.textContent = message;
      if (statusTextElement) statusTextElement.textContent = 'Estimasi waktu: 2-5 menit';
      if (queueInfoElement) queueInfoElement.classList.add('hidden');
      break;

    case 'completed':
      if (titleElement) titleElement.textContent = 'Assessment Selesai!';
      if (descriptionElement) descriptionElement.textContent = message;
      if (statusTextElement) statusTextElement.textContent = 'Mengarahkan ke hasil...';
      if (loadingSpinner) {
        loadingSpinner.classList.remove('animate-spin');
        loadingSpinner.innerHTML = '<svg class="w-16 h-16 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
      }
      break;

    case 'failed':
      if (titleElement) titleElement.textContent = 'Terjadi Kesalahan';
      if (descriptionElement) descriptionElement.textContent = message;
      if (statusTextElement) statusTextElement.textContent = 'Silakan coba lagi atau hubungi support';
      if (loadingSpinner) {
        loadingSpinner.classList.remove('animate-spin');
        loadingSpinner.innerHTML = '<svg class="w-16 h-16 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
      }
      break;

    case 'error':
      if (titleElement) titleElement.textContent = 'Kesalahan Sistem';
      if (descriptionElement) descriptionElement.textContent = message;
      if (statusTextElement) statusTextElement.textContent = 'Silakan refresh halaman atau hubungi support';
      break;
  }
}

// Check assessment status via API
async function checkAssessmentStatus() {
  const jobId = assessmentService.getCurrentJobId();
  if (!jobId) return;

  try {
    const statusResult = await assessmentService.getJobStatus(jobId);

    if (statusResult.success) {
      const jobData = statusResult.data;

      switch (jobData.status) {
        case 'queued':
          updateStatus('queued', 'Assessment Anda sedang dalam antrian untuk diproses.', { queuePosition: 'Unknown' });
          break;

        case 'processing':
          updateStatus('processing', 'AI sedang menganalisis data assessment Anda...');
          break;

        case 'completed':
          // Get the result
          if (jobData.result_id) {
            const resultData = await assessmentService.getResult(jobData.result_id);
            if (resultData.success && resultData.data.status === 'completed') {
              updateStatus('completed', 'Assessment berhasil diproses!');
              setTimeout(() => {
                navigateTo('result');
              }, 2000);
              return;
            }
          }
          break;

        case 'failed':
          updateStatus('failed', 'Terjadi kesalahan dalam pemrosesan assessment.');
          break;
      }
    }
  } catch (error) {
    console.error('Error checking assessment status:', error);
  }
}

function startAutoRefresh() {
  autoRefreshTimer = setInterval(() => {
    if (!websocketConnected) {
      checkAssessmentStatus();
    }
  }, 15000); // Check every 15 seconds if WebSocket is not connected
}

export function checkResults() {
  checkAssessmentStatus();
}

export function cleanupWaiting() {
  // Clear timers
  if (waitingTimer) clearInterval(waitingTimer);
  if (autoRefreshTimer) clearInterval(autoRefreshTimer);

  // Disconnect WebSocket
  if (websocketConnected) {
    websocketService.disconnect();
    websocketConnected = false;
  }
}
